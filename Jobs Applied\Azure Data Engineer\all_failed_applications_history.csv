Job ID,Job Link,<PERSON>sume Tried,Date listed,Date Tried,Assumed Reason,Stack Trace,External Job link,Screenshot Name
3996687464,https://www.linkedin.com/jobs/view/3996687464,GIRISH SAI THIRUVIDHULA_AzureDE.pdf,2024-08-15 10:33:22.657717,2024-09-05 10:33:47.596101,Problem in Easy Applying,Failed to click Submit application 😑,Easy Applied,Not Available
4018510220,https://www.linkedin.com/jobs/view/4018510220,Pending,2024-09-05 10:21:52.814877,2024-09-05 10:33:52.914127,Required experience is high,"
About the job
Contract Length- 6 Months (possibility of extension) 
Need to work in EST hours.
Mandatory Skills: Azure Databricks, Azure data lake, Ansible, SQL, Terraform, Kafka , Azure CLI, Databricks CLI, PowerShell and/or Bash.
 What Could Set You Apart
Must have designed the E2E architecture of unified data platform covering all the aspects of data lifecycle starting from Data Ingestion, Transformation, operations, and consumption.
To be successful in this role, this individual must be familiar with data integration, data modeling, data warehousing, big data technologies, and should be the subject matter expert.
Maintains close awareness of new and emerging technologies and their potential application for service offerings and products.
Experience in architecting and designing technical solutions for cloud-centric solutions based on industry standards using IaaS, PaaS, and SaaS capabilities.
Experience building and supporting mission-critical technology components with DR capabilities.
Experience with multi-tier system and service design & development for large enterprises
Exposure to infrastructure and application security technologies and approaches
Familiarity with requirements gathering techniques.
Experience in handling the key activities for the Enterprise Architecture practice for a specific sector.
Managing and mentoring architecture talent in the respective sector
Should have delivered architecture initiatives and showcased clear business efficiency in line with business strategies. Should have taken end-end responsibility & accountability in terms of architecture.
Should have delivered architecture roadmaps and develop delivery blueprints for the technology design.
Excellent communication skills and customer management skills
Should be able to deliver architectural initiatives in the space of digital, cloud, open-source technologies. Should have performed a similar role & capacity for large transformational engagements.
Hands on development Design and Develop applications using Databricks. Experience with Azure, AWS, or other cloud technologies
In depth understanding of Spark Architecture including Spark Core, Spark SQL, Data Frames, Spark Streaming, RDD caching, Spark MLib.
Good to have any programming language experience with Python/ SQL or Spark/Scala.
Strong understanding of Data Modeling and defining conceptual logical and physical data models.
 Required Qualifications
Must have excellent coding skills either Python or Scala, preferably Python.
Must have at least 10+ years of experience in architecture, design, implementation, and analytics solutions with total of 12+ years in Data Engineering domain.
  Must have designed and implemented at least 2-3 projects end-to-end in Databricks.
Must have at least 3+ years of experience in databricks which consists of various components as below.
Delta lake
dbConnect
db API 2.0
SQL Endpoint Photon engine
Unity Catalog
Databricks workflows orchestration
Security management
Platform governance
Data Security
Must have followed various architectural principles to design best suited per problem.
Must be well versed with Databricks Lakehouse concept and its implementation in enterprise environments.
Must have strong understanding of Data warehousing and various governance and security standards around Databricks.
Must have knowledge of cluster optimization and its integration with various cloud services.
Must have good understanding to create complex data pipelines.
Must be strong in SQL and spark-sql.
Must have strong performance optimization skills to improve efficiency and reduce cost.
Must have worked on designing both Batch and streaming data pipeline.
Must have extensive knowledge of Spark and Hive data processing framework.
Must have worked on any cloud (Azure, AWS, GCP) and most common services like ADLS/S3, ADF/Lambda, Cloud databases.
Must be strong in writing unit test case and integration test.
Must have strong communication skills and have worked with cross-platform teams.
Must have great attitude towards learning new skills and upskilling the existing skills.
Responsible for setting best practices around Databricks CI/CD.
Must understand composable architecture to take full advantage of Databricks capabilities.
Experience with machine learning tools such as mlFlow, Databricks AI/ML, Azure ML, AWS sagemaker, etc.
Experience in distilling complex technical challenges to actionable decisions for stakeholders and guiding project teams by building consensus and mediating compromises when necessary.
Experience coordinating the intersection of complex system dependencies and interactions.
Experience in solution delivery using common methodologies especially SAFe Agile but also Waterfall, Iterative, etc.
Preferred Qualifications
Good to have Rest API knowledge.
Good to have understanding around cost distribution.
Good to have knowledge on migration project to build Unified data platform.
Good to have knowledge of DBT.
Experience around DevSecOps including docker and Kubernetes.
Software development full lifecycle methodologies, patterns, frameworks, libraries, and tools
Knowledge of programming and scripting languages such as JavaScript, PowerShell, Bash etc.
Experience with data ingestion technologies such as Azure Data Factory, SSIS, Pentaho, Alteryx.
Experience with visualization tools such as Tableau, Power BI.
Demonstrated knowledge of relevant industry trends and standards

Experience required 12 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4014732645,https://www.linkedin.com/jobs/view/4014732645,GIRISH SAI THIRUVIDHULA_AzureDE.pdf,2024-09-03 10:33:55.815535,2024-09-05 10:33:59.322730,Problem in Easy Applying,Failed to click Submit application 😑,Easy Applied,Not Available
4004453428,https://www.linkedin.com/jobs/view/4004453428,Pending,2024-08-22 10:34:02.418933,2024-09-05 10:34:02.517988,Required experience is high,"
About the job
We are
At Synechron, we believe in the power of digital to transform businesses for the better. Our global consulting firm combines creativity and innovative technology to deliver industry-leading digital solutions. Synechron’s progressive technologies and optimization strategies span end-to-end Artificial Intelligence, Consulting, Digital, Cloud & DevOps, Data, and Software Engineering, servicing an array of noteworthy financial services and technology firms. Through research and development initiatives in our FinLabs we develop solutions for modernization, from Artificial Intelligence and Blockchain to Data Science models, Digital Underwriting, mobile-first applications and more. Over the last 20+ years, our company has been honored with multiple employer awards, recognizing our commitment to our talented teams. With top clients to boast about, Synechron has a global workforce of 14,000+, and has 55 offices in 20 countries within key global markets.


Role: Talend/Qlik Developer
Location: Montreal, QC
Type: Full-time 

A Talend Developer will be responsible for building solution in Talend studio based on business requirements and technical specifications. Implement data integration workflows for extracting transforming and loading the data from various sources into the databases, warehouse, or data lakes. 
The ideal candidate should have the skills listed below but in addition should be a self-driven, dedicated individual who works well in a team and thinks and acts strategically.

Responsibilities:
• Estimation and end to end execution of required changes, coordination with stakeholders as required
• Responsible for the development, maintenance and support of multiple applications or frameworks
• Translate high-level business requirements on multiple, complex requests into detailed functional, technical, and/or system specifications
• Test software designs and solutions (including unit testing and integrated testing)
• Build and maintain automated test suites for applications
• Support and respond to user queries or issues; determine the root cause and best resolution of escalated issues
• Work with data management team and data providers to communicate and define any data (including data quality) and reporting requirements

Required technical Skills:
• At least 5+ years of hands-on application development utilizing ETL tools like Talend.
• Perform data profiling, cleansing and validation to ensure data accuracy and quality.
• Optimize Talend job performance and scalability, considering large volume of data.
• Good to have prior hands-on development experience on Java 8+, python, shell scripting.
• Experience in the development of service-oriented architecture application, good understanding of Object-Oriented Design and Design Patterns.
• Solid understanding of DB concepts and working with relational databases (DB2, Sybase or other).
• A strong understanding of SDLC principles and experience delivering in Agile/Scrum.
• Strong analytical and design skills, including the ability to understand business requirements and translate them into efficient and effective technical designs that work well within large-scale, well-structured enterprise environments.

Desired Skills:
• Knowledge of Financial services.
• Experience with Angular and/or other modern frameworks would be a plus.
• Knowledge on informatica will plus.
• Experience with Data visualization tools and reporting tools.
• Experience with Cloud based tools and technologies.
• UI Design experience, specifically for web applications.
• Unix OS, Scripting, Python or Perl.
• Proficient in DevOps tooling and practices.

We can offer you:

A highly competitive compensation and benefits package
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
Laptop and a mobile phone
15 days of paid annual leave (plus sick leave and national holidays)
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability (plans vary by region)
Retirement savings plans
A higher education certification policy
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture


SYNE CHRON'S DIVERSITY & INCLUSION STATEMENT
Diversity and inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative-action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity, and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements, and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Experience required 5 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4018510220,https://www.linkedin.com/jobs/view/4018510220,Pending,2024-09-05 10:22:23.560121,2024-09-05 10:40:23.656740,Required experience is high,"
About the job
Contract Length- 6 Months (possibility of extension) 
Need to work in EST hours.
Mandatory Skills: Azure Databricks, Azure data lake, Ansible, SQL, Terraform, Kafka , Azure CLI, Databricks CLI, PowerShell and/or Bash.
 What Could Set You Apart
Must have designed the E2E architecture of unified data platform covering all the aspects of data lifecycle starting from Data Ingestion, Transformation, operations, and consumption.
To be successful in this role, this individual must be familiar with data integration, data modeling, data warehousing, big data technologies, and should be the subject matter expert.
Maintains close awareness of new and emerging technologies and their potential application for service offerings and products.
Experience in architecting and designing technical solutions for cloud-centric solutions based on industry standards using IaaS, PaaS, and SaaS capabilities.
Experience building and supporting mission-critical technology components with DR capabilities.
Experience with multi-tier system and service design & development for large enterprises
Exposure to infrastructure and application security technologies and approaches
Familiarity with requirements gathering techniques.
Experience in handling the key activities for the Enterprise Architecture practice for a specific sector.
Managing and mentoring architecture talent in the respective sector
Should have delivered architecture initiatives and showcased clear business efficiency in line with business strategies. Should have taken end-end responsibility & accountability in terms of architecture.
Should have delivered architecture roadmaps and develop delivery blueprints for the technology design.
Excellent communication skills and customer management skills
Should be able to deliver architectural initiatives in the space of digital, cloud, open-source technologies. Should have performed a similar role & capacity for large transformational engagements.
Hands on development Design and Develop applications using Databricks. Experience with Azure, AWS, or other cloud technologies
In depth understanding of Spark Architecture including Spark Core, Spark SQL, Data Frames, Spark Streaming, RDD caching, Spark MLib.
Good to have any programming language experience with Python/ SQL or Spark/Scala.
Strong understanding of Data Modeling and defining conceptual logical and physical data models.
 Required Qualifications
Must have excellent coding skills either Python or Scala, preferably Python.
Must have at least 10+ years of experience in architecture, design, implementation, and analytics solutions with total of 12+ years in Data Engineering domain.
  Must have designed and implemented at least 2-3 projects end-to-end in Databricks.
Must have at least 3+ years of experience in databricks which consists of various components as below.
Delta lake
dbConnect
db API 2.0
SQL Endpoint Photon engine
Unity Catalog
Databricks workflows orchestration
Security management
Platform governance
Data Security
Must have followed various architectural principles to design best suited per problem.
Must be well versed with Databricks Lakehouse concept and its implementation in enterprise environments.
Must have strong understanding of Data warehousing and various governance and security standards around Databricks.
Must have knowledge of cluster optimization and its integration with various cloud services.
Must have good understanding to create complex data pipelines.
Must be strong in SQL and spark-sql.
Must have strong performance optimization skills to improve efficiency and reduce cost.
Must have worked on designing both Batch and streaming data pipeline.
Must have extensive knowledge of Spark and Hive data processing framework.
Must have worked on any cloud (Azure, AWS, GCP) and most common services like ADLS/S3, ADF/Lambda, Cloud databases.
Must be strong in writing unit test case and integration test.
Must have strong communication skills and have worked with cross-platform teams.
Must have great attitude towards learning new skills and upskilling the existing skills.
Responsible for setting best practices around Databricks CI/CD.
Must understand composable architecture to take full advantage of Databricks capabilities.
Experience with machine learning tools such as mlFlow, Databricks AI/ML, Azure ML, AWS sagemaker, etc.
Experience in distilling complex technical challenges to actionable decisions for stakeholders and guiding project teams by building consensus and mediating compromises when necessary.
Experience coordinating the intersection of complex system dependencies and interactions.
Experience in solution delivery using common methodologies especially SAFe Agile but also Waterfall, Iterative, etc.
Preferred Qualifications
Good to have Rest API knowledge.
Good to have understanding around cost distribution.
Good to have knowledge on migration project to build Unified data platform.
Good to have knowledge of DBT.
Experience around DevSecOps including docker and Kubernetes.
Software development full lifecycle methodologies, patterns, frameworks, libraries, and tools
Knowledge of programming and scripting languages such as JavaScript, PowerShell, Bash etc.
Experience with data ingestion technologies such as Azure Data Factory, SSIS, Pentaho, Alteryx.
Experience with visualization tools such as Tableau, Power BI.
Demonstrated knowledge of relevant industry trends and standards

Experience required 12 > Current Experience 1. Skipping this job!
",Skipped,Not Available
3977318660,https://www.linkedin.com/jobs/view/3977318660,Pending,2024-08-06 10:40:33.516043,2024-09-05 10:40:33.623156,Required experience is high,"
About the job
About Us:
LTIMindtree is a global technology consulting and digital solutions company that enables enterprises across industries to reimagine business models, accelerate innovation, and maximize growth by harnessing digital technologies. As a digital transformation partner to more than 700+ clients, LTIMindtree brings extensive domain and technology expertise to help drive superior competitive differentiation, customer experiences, and business outcomes in a converging world. Powered by nearly 90,000 talented and entrepreneurial professionals across more than 30 countries, LTIMindtree — a Larsen & Toubro Group company — combines the industry-acclaimed strengths of erstwhile Larsen and Toubro Infotech and Mindtree in solving the most complex business challenges and delivering transformation at scale. For more information, please visit www.ltimindtree.com.
 Job Title: 
ETL Developer

Work Location
Markham Canada

Job Description:
Minimum 8 years of experience in IT or data management and analytics experience required 
8 years of strong experience in Informatica power Centre 
5 years of experience working on any relational databases like Oracle Teradata PostgreSQL etc 
Strong SQL writing skills
Should have deep knowledge on performance tuning of ETL Job SQLs Partitioning Indexing and various other techniques
Strong Experience in Data warehousing concepts
Experience in Build and release planning
Should have exposure to understand the relationship between data and business outcomes and can focus on long term strategies for data
Must be able to identify any gaps in the business requirement and assist Business Analyst to rectify the gaps 
Support high level end to end architecture design from business requirement for ODS Enterprise data warehouse type systems
Maintain reconciliation between systems to facilitate audit trail 
Familiar with the Agile software development 
Strong mentoring and coaching skills and ability to lead by example for junior team members 
Outcome focused with strong decision making and critical thinking skills
Excellent verbal and written communication skills 

Secondary Skill
 PLSQL
 Experience in writing Shell scripts
 Experience of working with Insurance Domain 
 Agile Scrum Experience
 Benefits/perks listed below may vary depending on the nature of your employment with LTIMindtree Canada (“LTIMC”):

Benefits and Perks:
Comprehensive Medical Plan Covering Medical, Dental, Vision
Health Care Spending Account
Short Term and Long-Term Disability Coverage
Life Insurance
Annual vacation and other Paid Leaves
Maternity Leave Top Up Pay

The range displayed on each job posting reflects the minimum and maximum salary target for the position across all Canada locations. Within the range, individual pay is determined by work location and job level and additional factors including job-related skills, experience, and relevant education or training. Depending on the position offered, other forms of compensation may be provided as part of overall compensation like an annual performance-based bonus, sales incentive pay and other forms of bonus or variable compensation.

Disclaimer: The compensation and benefits information provided herein is accurate as of the date of this posting.

LTIMindtree is an equal opportunity employer that is committed to diversity in the workplace. Our employment decisions are made without regard to race, color, creed, religion, sex (including pregnancy, childbirth or related medical conditions), gender identity or expression, national origin, ancestry, age, family-care status, veteran status, marital status, civil union status, domestic partnership status, military service, handicap or disability or history of handicap or disability, genetic information, atypical hereditary cellular or blood trait, union affiliation, affectional or sexual orientation or preference, or any other characteristic protected by applicable federal, state, or local law, except where such considerations are bona fide occupational qualifications permitted by law.
 
 Safe return to office:
In order to comply with LTIMindtree’ s company COVID-19 vaccine mandate, candidates must be able to provide proof of full vaccination against COVID-19 before or by the date of hire. Alternatively, one may submit a request for reasonable accommodation from LTIMindtree’s COVID-19 vaccination mandate for approval, in accordance with applicable state and federal law, by the date of hire. Any request is subject to review through LTIMindtree’s applicable processes.

Experience required 8 > Current Experience 1. Skipping this job!
",Skipped,Not Available
3758943023,https://www.linkedin.com/jobs/view/3758943023,Pending,2024-08-15 10:40:36.829146,2024-09-05 10:40:37.003176,Required experience is high,"
About the job
Role: Data Warehouse Engineer (Pyspark/ Python, Azure and Snowflake)

Responsibilities:
Strong understanding or Snowflake on Azure Architecture, design, implementation and operationalization of large-scale data and analytics solutions on Snowflake Cloud Data Warehouse.
Hands-on development experience with Snowflake features such as Snow SQL; Snow Pipe; Python; Tasks; Streams; Time travel; Zero Copy Cloning; Optimizer; Metadata Manager; data sharing; and stored procedures.
Experience in Data warehousing - OLTP, OLAP, Dimensions, Facts, and Data modeling.
Need to have working knowledge of MS Azure configuration items with respect to Snowflake.
Developing EL pipelines in and out of data warehouse using combination of Data bricks, Python and Snow SQL.
Developing scripts UNIX, Python etc. to Extract, Load and Transform data, as well as other utility functions.
Provide production support for Data Warehouse issues such data load problems, transformation translation problems

Requirements: 
You are: 
Minimum 10 years of designing and implementing an operational production grade large-scale data solution on Microsoft Azure Snowflake Data Warehouse.
Including hands on experience with productionized data ingestion and processing pipelines using Python, Data bricks, Snow SQL
Excellent understanding of Snowflake Internals and integration of Snowflake with other data processing and reporting technologies

It would be great if you also had:
Experience with Snowflake
Detail-oriented, ability to turn deliverables around quickly with a high degree of accuracy
Strong analytical skills, ability to interpret business requirements and produce functional and technical design documents
Good time management skills – Ability to prioritize and multi-task, handling multiple efforts at once
Strong desire to understand and learn domain.
Experience in a financial services/banking industry
Ability to work in a fast paced environment; to be flexible and learn quickly.
Ability to multi-task with attention to detail/ prioritize tasks.

We can offer you:
A highly competitive compensation and benefits package
A multinational organization with 51 offices in 20 countries and the possibility to work abroad
Laptop and a mobile phone
15 days of paid annual leave (plus national holidays)
Maternity & Paternity leave plans
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability
RRSP with employer’s contribution
A higher education certification policy
Comprehensive Relocation Expense Coverage
Commuter benefits
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Le rôle : Développeur Intégrateur Snowflake (Pyspark/ Python, Azure and Snowflake)

Responsabilités :
Solide compréhension de Snowflake sur Azure. Architecture, conception, mise en œuvre et opérationnalisation de solutions de données et d'analyse à grande échelle sur Snowflake Cloud Data Warehouse.
Expérience pratique du développement avec les fonctionnalités de Snowflake telles que Snow SQL, Snow Pipe, Python, Tâches, Flux, Time Travel, Clonage Zero Copy, Optimiseur, Gestionnaire de métadonnées, partage de données et procédures stockées.
Expérience en entreposage de données - OLTP, OLAP, Dimensions, Faits et modélisation des données.
Il est nécessaire d'avoir une connaissance pratique des éléments de configuration de MS Azure par rapport à Snowflake.
Développement de pipelines EL entrants et sortants de l'entrepôt de données en utilisant une combinaison de Data bricks, Python et Snow SQL.
Développement de scripts UNIX, Python, etc. pour extraire, charger et transformer les données, ainsi que d'autres fonctions utilitaires.
Fournir un support de production pour les problèmes d'entrepôt de données tels que les problèmes de chargement de données, les problèmes de traduction de transformation.
Comprendre les pipelines de données et les moyens modernes d'automatiser les pipelines de données à l'aide de tests basés sur le cloud et documenter clairement les mises en œuvre, de manière à ce que d'autres puissent facilement comprendre les exigences, la mise en œuvre et les conditions de test.

Exigences :
Vous êtes :
Au minimum 10 ans d'expérience dans la conception et la mise en œuvre d'une solution de données à grande échelle en production opérationnelle sur Microsoft Azure Snowflake Data Warehouse.
Expérience pratique de l'ingestion et du traitement de données en production à l'aide de Python, Data bricks, Snow SQL.
Excellentes compétences en présentation et en communication, à la fois écrites et verbales, capacité à résoudre des problèmes et à concevoir dans un environnement aux exigences peu claires.

Ce serait génial si vous aviez également :
Souci du détail, capacité à produire rapidement des livrables avec un haut degré de précision.
Solides compétences analytiques, capacité à interpréter les besoins commerciaux et à produire des documents de conception fonctionnelle et technique.
Bonnes compétences en gestion du temps - Capacité à hiérarchiser et à effectuer plusieurs tâches en même temps.
Fort désir de comprendre et d'apprendre le domaine.
Expérience dans le secteur des services financiers/bancaires.

Nous pouvons vous offrir :
Un package de rémunération et d'avantages sociaux très compétitif.
Une organisation multinationale avec 52 bureaux dans 20 pays et la possibilité de travailler à l'étranger.
Un ordinateur portable et un téléphone mobile.
15 jours de congé annuel payé (plus les jours fériés nationaux).
Plans de congé de maternité et de paternité.
Un plan d'assurance complet comprenant : assurance maladie, dentaire, vision, assurance vie, invalidité à long terme/court terme.
REER avec contribution de l'employeur.
Une politique de certification de l'enseignement supérieur.
Une couverture complète des frais de déménagement.
Avantages liés au transport.
De nombreuses opportunités de formation axées sur les compétences, les connaissances substantielles et le développement personnel.
Udemy à la demande pour les entreprises pour tous les employés de Synechron, avec un accès gratuit à plus de 5000 cours sélectionnés.
Possibilités de coaching avec des collègues expérimentés de nos laboratoires d'innovation financière (FinLabs) et de nos groupes de centres d'excellence (CoE).
Projets de pointe auprès des principales banques de niveau un, des institutions financières et des compagnies d'assurance au monde.
Une organisation accessible et conviviale.
Une culture de travail vraiment diversifiée, amusante et mondiale.

DÉCLARATION DE DIVERSITÉ ET D'INCLUSION DE SYNECHRON
La diversité et l'inclusion sont fondamentales pour notre culture, et Synechron est fier d'être un lieu de travail égalitaire et un employeur pratiquant l'action positive. Notre initiative de diversité, d'équité et d'inclusion (DEI) «‘Same Difference’» s'engage à favoriser une culture inclusive - promouvoir l'égalité, la diversité et un environnement respectueux envers tous. Nous croyons fermement qu'une main-d'œuvre diversifiée contribue à renforcer les entreprises avec succès en tant qu'entreprise mondiale. Nous encourageons les candidats de divers horizons, qu'il s'agisse de race, d'origine ethnique, de religion, d'âge, de statut matrimonial, de genre, d'orientation sexuelle ou de handicap, à postuler. Nous autonomisons notre main-d'œuvre mondiale en proposant des arrangements de travail flexibles, du mentorat, une mobilité interne, des programmes d'apprentissage et de développement, et bien plus encore.
Toutes les décisions d'emploi chez Synechron sont basées sur les besoins de l'entreprise, les exigences du poste et les qualifications individuelles, sans tenir compte du genre, de l'identité de genre, de l'orientation sexuelle, de la race, de l'origine ethnique, du handicap ou du statut de vétéran du candidat, ou de toute autre caractéristique protégée par la loi.

Experience required 10 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4004453428,https://www.linkedin.com/jobs/view/4004453428,Pending,2024-08-22 10:40:39.913494,2024-09-05 10:40:40.006413,Required experience is high,"
About the job
We are
At Synechron, we believe in the power of digital to transform businesses for the better. Our global consulting firm combines creativity and innovative technology to deliver industry-leading digital solutions. Synechron’s progressive technologies and optimization strategies span end-to-end Artificial Intelligence, Consulting, Digital, Cloud & DevOps, Data, and Software Engineering, servicing an array of noteworthy financial services and technology firms. Through research and development initiatives in our FinLabs we develop solutions for modernization, from Artificial Intelligence and Blockchain to Data Science models, Digital Underwriting, mobile-first applications and more. Over the last 20+ years, our company has been honored with multiple employer awards, recognizing our commitment to our talented teams. With top clients to boast about, Synechron has a global workforce of 14,000+, and has 55 offices in 20 countries within key global markets.


Role: Talend/Qlik Developer
Location: Montreal, QC
Type: Full-time 

A Talend Developer will be responsible for building solution in Talend studio based on business requirements and technical specifications. Implement data integration workflows for extracting transforming and loading the data from various sources into the databases, warehouse, or data lakes. 
The ideal candidate should have the skills listed below but in addition should be a self-driven, dedicated individual who works well in a team and thinks and acts strategically.

Responsibilities:
• Estimation and end to end execution of required changes, coordination with stakeholders as required
• Responsible for the development, maintenance and support of multiple applications or frameworks
• Translate high-level business requirements on multiple, complex requests into detailed functional, technical, and/or system specifications
• Test software designs and solutions (including unit testing and integrated testing)
• Build and maintain automated test suites for applications
• Support and respond to user queries or issues; determine the root cause and best resolution of escalated issues
• Work with data management team and data providers to communicate and define any data (including data quality) and reporting requirements

Required technical Skills:
• At least 5+ years of hands-on application development utilizing ETL tools like Talend.
• Perform data profiling, cleansing and validation to ensure data accuracy and quality.
• Optimize Talend job performance and scalability, considering large volume of data.
• Good to have prior hands-on development experience on Java 8+, python, shell scripting.
• Experience in the development of service-oriented architecture application, good understanding of Object-Oriented Design and Design Patterns.
• Solid understanding of DB concepts and working with relational databases (DB2, Sybase or other).
• A strong understanding of SDLC principles and experience delivering in Agile/Scrum.
• Strong analytical and design skills, including the ability to understand business requirements and translate them into efficient and effective technical designs that work well within large-scale, well-structured enterprise environments.

Desired Skills:
• Knowledge of Financial services.
• Experience with Angular and/or other modern frameworks would be a plus.
• Knowledge on informatica will plus.
• Experience with Data visualization tools and reporting tools.
• Experience with Cloud based tools and technologies.
• UI Design experience, specifically for web applications.
• Unix OS, Scripting, Python or Perl.
• Proficient in DevOps tooling and practices.

We can offer you:

A highly competitive compensation and benefits package
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
Laptop and a mobile phone
15 days of paid annual leave (plus sick leave and national holidays)
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability (plans vary by region)
Retirement savings plans
A higher education certification policy
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture


SYNE CHRON'S DIVERSITY & INCLUSION STATEMENT
Diversity and inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative-action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity, and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements, and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Experience required 5 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4004868017,https://www.linkedin.com/jobs/view/4004868017,Pending,2024-08-22 10:40:42.710382,2024-09-05 10:40:42.752832,Required experience is high,"
About the job
Job Title: ETL developer with IICS and Snowflake
 Location: Toronto, ON 
 Employment Type: (Full-time )

Skills Must have: 
 9+ years Exp
 Etl,
 IICS,
 Snowflake
 Informatica

Experience required 9 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4015038087,https://www.linkedin.com/jobs/view/4015038087,Pending,2024-09-04 12:40:52.856820,2024-09-05 10:40:52.955871,Required experience is high,"
About the job
This is a primarily remote role in Vancouver. Candidates must be in British Columbia or Winnipeg to be considered for this role. 


In this role, you will join a stable and tenured team, and a large company in the retail industry. You will be a generalist; you will mainly design and develop ETL processes, and part of your work will be data analysis. 


This company is in the process of migrating to the cloud, and integrating a large variety of data sources and internal applications. They are in the early stages of their data integration work, and you will help with the planning and architecture. You will help with platform selection as well. 


If you have experience with large-scale data integration projects and migrations to the cloud, we encourage you to apply!

 What you will do and how you will make an impact … 


You will work with a team of Programmer Analysts, Business Analysts, Solutions Architects and QAs. 

You will work with a mix of sales, inventory and internal operations data. 


You will work with a variety of data sources, and will work collaboratively with the team to plan the data integration.


You will design and develop ETL processes. 


You will work with SQL databases and will use Power BI for reporting. 


Qualifications
  Minimum 3 years of experience in a Data Engineer / ETL Developer role 


Previous experience with relational databases 


Previous experience supporting a large data integration project / supporting an organization’s migration to the cloud 


Does this sound like it was written for you? Excellent! Please apply and let’s explore this together. 


The interview process … 


A bit about myself - my name is Tanvi Krishna, and I am a recruitment consultant based in Vancouver, BC. I am constantly working on IT and creative roles, and I am always looking to meet new people. 


If you're interested in pursuing this role, please apply to this posting. If you are selected for the next stage, I will contact you for an initial discussion. This will be a chance for us to discuss the job requirements in greater detail, as well as your career goals and preferences for your next position. We can also discuss other opportunities which may fit what you're looking for. 


Please feel free to reach out and find me on LinkedIn by searching my name: Tanvi Krishna


Compensation & benefits … 


This is a full-time and permanent position that includes a competitive base salary, extended health and dental benefits, and paid vacation.

Experience required 3 > Current Experience 1. Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Previous resume,2024-09-01 10:41:29.632158,2024-09-05 10:44:07.497702,Problem in Easy Applying,"Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=128.0.6613.120); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x01078213+26163]
	(No symbol) [0x01009CC4]
	(No symbol) [0x00F024C3]
	(No symbol) [0x00F08064]
	(No symbol) [0x00F09948]
	(No symbol) [0x00F099C0]
	(No symbol) [0x00F470C5]
	(No symbol) [0x00F4762B]
	(No symbol) [0x00F3D531]
	(No symbol) [0x00F6AD04]
	(No symbol) [0x00F3D445]
	(No symbol) [0x00F6AF54]
	(No symbol) [0x00F84661]
	(No symbol) [0x00F6AA56]
	(No symbol) [0x00F3BE89]
	(No symbol) [0x00F3C8CD]
	GetHandleVerifier [0x0134D313+2996019]
	GetHandleVerifier [0x013A1B89+3342249]
	GetHandleVerifier [0x01107AEF+614159]
	GetHandleVerifier [0x0110F17C+644508]
	(No symbol) [0x010127FD]
	(No symbol) [0x0100F6F8]
	(No symbol) [0x0100F895]
	(No symbol) [0x01001C16]
	BaseThreadInitThunk [0x75B37BA9+25]
	RtlInitializeExceptionChain [0x77D8C10B+107]
	RtlClearBits [0x77D8C08F+191]
",Easy Applied,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2024-08-22 10:44:29.224389,2024-09-05 10:44:29.291458,Required experience is high,"
About the job
Job Title: 
SQL Developer

Location: 
Toronto, Canada


Must Local to Toronto Area, Canada

Responsibilities: 
Need 6+ Years in SQL
Write complex SQL queries
Understand data governance policies and procedures and develop SQL queries to check data is in compliance
Understand data models and data relations to develop SQL queries
Conduct data analysis and generate reports on banking data using SQL query programming
Write complex SQL queries to verify data governance policies and procedures
Ensure compliance with data privacy regulations and standards
Experience working with SaaS and R is preferred
Knowledge of data governance frameworks and best practices
Proficiency in SQL and experience with relational databases such as Oracle, MySQL, PostgreSQL, or MS SQL Server
Collaborate with development teams to resolve and data compliance issues

 Qualifications: 
Bachelor’s degree in Computer Science or any related fields

Required Skills: 
Experience working with SaaS and R is preferred


Knowledge of data governance frameworks and best practices


Proficiency in SQL and experience with relational databases such as Oracle, MySQL, PostgreSQL, or MS SQL Server

Preferred Skills: 
Experience working with SaaS and R

Experience required 6 > Current Experience 1. Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2024-08-22 10:45:35.190978,2024-09-05 10:45:35.243355,Required experience is high,"
About the job
Integration Solution Architect - Leading Consultancy Firm - £85-£100k + Package

Client facing Solution Architect required by a leading consultancy firm who specialise in delivering large scale digital transformation and integration programmes.

This role would suit an experienced solution architect who has some experience or knowledge working on integration programmes previously. A knowledge of iPaaS solutions such as Mulesoft, Snaplogic or similar would be beneficial.

The solution architect will work closely with client stakeholders to define solutions, and will then work with internal teams of developers to ensure that technical solutions are meeting the needs of the client.

This role would suit an individual who enjoys being hands-on when required rather than just focusing on high level enterprise architecture.

The Solutions Architect will be faced with the following responsibilities:

Deliver a target operating model for the integration platform, with a solution design that meets the business and technical requirements for an API management pilot
Management of an MVP delivery phase including architectural and delivery governance
Provide technical guidance and mentorship to development teams, making sure that the architectural designs are successfully implemented

The Senior Solution Architect will have the following skills/experience:

10+ years’ experience working as a Solution Architect
Strong Pharmaceutical, FMCG, or Consultancy Background
Proven Experience working in iPaaS Solutions
Familiarity with Integration Tools (MuleSoft, Snap logic, Boomi)
Ideally, experience working with TOGAF
Excellent Client-Facing Skills

The company is based in Central London and works directly with numerous large-scale clients, across multiple industries and specializes in systems integration.

This is a hybrid role with 1-2 days a week on-site in Central London when required.

Experience required 10 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4017608717,https://www.linkedin.com/jobs/view/4017608717,Pending,2024-09-04 16:45:59.229432,2024-09-05 10:45:59.283061,Asking for Security clearance,"
About the job
Solution Architect – API
Reliability Clearance or Secret clearance is required.
2 locations: Toronto and Ottawa
both are hybrid locations

SCOPE
The Solution Architect is responsible for designing, developing, and implementing technology solutions that meet business requirements and align with the organization’s strategic goals. This role involves working closely with stakeholders, including business leaders, developers, and project managers, to ensure that solutions are technically sound, scalable, and cost-effective. The Solution Architect provides technical leadership throughout the project lifecycle, from initial concept through to implementation and support.
DUTIES
Provide API governance and EDI partner management based on IBM APIM, APIC, ACE and EDI technologies with consideration given to kakfa/confluent and other event processing technologies.
Will need to work with business to do discovery and IT reviews
The mandate at CPC is to be API first, a roadmap needs to align the EDI strategy with partner integration and api exposure. There is 3 big projects that will need guidance and technology roadmaps ecommerce platform, shopify , elink .

Technical Skills
• API Management: Expertise in API design, development, and management, including knowledge of API gateways, security, and monitoring.
• EDI Integration: Understanding of Electronic Data Interchange (EDI) standards, protocols, and tools for seamless partner integration.
• Integration Technologies: Proficiency in integration platforms such as IBM DataPower, IBM MQ, IBM ACE, IBM APIC, SOA Suite, and IBM APIM.
• Partner integration with third party SaaS providers using SAML, third party SDKs ( ie. Blueink),
• Security: Knowledge of cybersecurity principles to ensure secure API and EDI transactions.
• Cloud Services: Familiarity with cloud platforms (AWS, Azure, Google Cloud) and their integration capabilities.
• Knowledge of web service protocols: Rest ( XML/JSON), soap, Graphql, webhooks and authentication/autorizaiton-oauth-jwt mechanisms.

Found ""Clearance"" or ""Polygraph"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2024-08-22 10:46:02.094921,2024-09-05 10:46:02.200351,Required experience is high,"
About the job
Job Title: IT Presales Solution Architect
Location: Canada (Remote)
Job Type: Full-Time

Job Summary:
As a Solution Architect specializing in infrastructure design involving Private and Hyperscale Cloud services, you will play a pivotal role in driving success in the TELUS International Digital Solutions business. You will work closely with the sales team, clients, and technical teams to design, architect, and implement cutting-edge solutions tailored to our clients' needs, and leveraging TELUS International Digital Services solutions as your building blocks. Your expertise will bridge the gap between customer requirements and technical solutions, ensuring that our offerings align with TELUS International business goals and deliver exceptional customer value.

Key Responsibilities:

Solution Design & Architecture: Collaborate with sales teams to understand customer requirements and design solutions that leverage TELUS International Digital Solutions services across public, private, and hybrid cloud models
Technical Pre-Sales Support: Provide technical expertise during the sales process by crafting tailored presentations, conducting demonstrations, and answering technical questions to effectively communicate the value proposition of our solutions.
Client Engagement: Engage with clients to gather technical requirements, assess current infrastructure, and propose solutions that align with their business objectives.
Collaboration: Work closely with product management, engineering, and service delivery teams to ensure proposed solutions are viable, scalable, and align with the company's capabilities and offerings.
Strategy: Develop and propose comprehensive strategies that encompass TELUS International Digital Solutions services (that leverage public, private, and hybrid cloud models, ensuring scalability, performance, and cost-effectiveness)
Market & Industry Insights: Stay updated on industry trends, emerging technologies, and competitive landscapes related to private and hyperscale clouds to ensure that our offerings remain competitive and innovative.
Documentation: Develop and maintain comprehensive documentation of proposed solutions, including architecture diagrams, technical specifications, and implementation plans.
Training & Enablement: Provide training and enablement sessions to internal teams and clients to ensure a deep understanding of the solutions being offered and implemented.

Qualifications:

Education: Bachelor’s degree in Computer Science, Engineering, Information Technology, or related field. Master’s degree is a plus.
Experience:
Minimum of 10+ years of experience in a solution architect or pre-sales role, with a focus on costing and pricing solutions for customer requests / RFP responses
Experience with hyperscale cloud providers (AWS, Azure, Google Cloud).
Strong expertise in private cloud environments (ideally VMware Cloud Director)
Skills:
In-depth knowledge of premise-based and cloud architectures, network design, and security best practices.
Excellent communication, presentation skills and sales proposal writing, with the ability to articulate complex technical concepts to non-technical stakeholders.
Strong problem-solving skills with a customer-centric approach.
Ability to work independently and manage multiple sales solution opportunities simultaneously.

Certifications:
Relevant certifications from AWS, Azure, Google Cloud, VMware, or other industry-recognized organizations are highly desirable.

Preferred Attributes:

A passion for staying ahead of the curve in cloud technologies and IT solutions, continuously seeking innovative approaches to address client challenges.
Collaborative mindset with the ability to work effectively across teams and departments.
Results-driven focus on delivering results and achieving business objectives through technical excellence and customer satisfaction.
Ability to prioritize and organize effectively.
Excellent written and verbal communication skills.
Excellent analytical and troubleshooting abilities.

Experience required 10 > Current Experience 3. Skipping this job!
",Skipped,Not Available
4009476744,https://www.linkedin.com/jobs/view/4009476744,Pending,2024-09-04 17:46:21.252869,2024-09-05 10:46:21.352779,Required experience is high,"
About the job
About Ascendion:
Ascendion is a full-service digital engineering solutions company. We make and manage software platforms and products that power growth and deliver captivating experiences to consumers and employees. Our engineering, cloud, data, experience design, and talent solution capabilities accelerate transformation and impact for enterprise clients. Headquartered in New Jersey, our workforce of 6,000+ Ascenders delivers solutions from around the globe. Ascendion is built differently to engineer the next.

We have a culture built on opportunity, inclusion and a spirit of partnership. Come, change the world with us:
Build the coolest tech for the world’s leading brands
Solve complex problems – and learn new skills
Experience the power of transforming digital engineering for Fortune 500 clients
Master your craft with leading training programs and hands-on experience

Experience a community of change-makers!
Join a culture of high-performing innovators with endless ideas and a passion for tech. Our culture is the fabric of our company, and it is what makes us unique and diverse. The way we share ideas, learning, experiences, successes, and joy allows everyone to be their best at Ascendion.

About the Role:
Title: Customer Engineer/Solution Architect for VMWare to Hyper-V Migrations
Location: 100% remote

We are seeking a skilled and experienced Architect with expertise in Windows Server, SCVMM, Hyper-V, and VMWare. The successful candidate needs to have a number of years of experience with VMWare to Hyper-V Migrations.

Role type: Advisory - architecture, consulting, and support to help customers drive solutions and resolutions!

Qualifications:
Technical experience with hybrid infrastructures, architecture designs, and technology management.
8+ years’ experience with customer facing roles in systems architecture, administration, operations, software support, IT consulting, on-premises and cloud support or consulting for enterprise and/or government customer base, and advanced troubleshooting
Understanding of Modern IT Architecture and Strategy
Proven track record in successfully planning, deploying, operating, and optimizing on-premises and cloud environments
Windows and Azure certifications.
Candidates must be able to work independently.
Characteristics of importance include but are not limited to customer service, problem solving, communication skills, and the ability to read/interpret the needs/wants of customer executives and leaders.
BA/BS (or higher) in Information Technology or equivalent experience.
Expert level of enterprise software product offerings.
Excellent customer service skills.
NATIVE/BILINGUAL FRENCH SPEAKERS IS A MUST HAVE.

Skill Set:
Windows Server (Windows Server 2012 R2-2025 = advanced to expert level)
Virtualization (migration from VMWare to the following, Hyper-V, Azure VMWare Solutions, Azure Stack HCI, Azure VMs)
System Center Virtual Machine Manager
Failover Clustering
Windows Performance
Azure Arc
Azure Backup
Azure Site Recovery
General Azure IaaS platform (networking, storage, etc.)

Salary Range: The salary for this position is between CAD 120000- 150000 Annually. Factors affecting pay within this range may include geography/market, skills, education, experience, and other qualifications of the successful candidate.

Benefits: The Company offers the following benefits for this position, subject to applicable eligibility requirements: [medical insurance] [dental insurance] [vision insurance] [10 days of paid time off + holidays]

Want to change the world? Let us know.

Tell us about your experiences, education, and ambitions. Bring your knowledge, unique viewpoint, and creativity to the table. Let’s talk!

Experience required 8 > Current Experience 3. Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2024-09-04 17:46:34.254442,2024-09-05 10:46:34.326881,Asking for Security clearance,"
About the job
Reliability or secret clearance is required 
Office location: Preference is Ottawa, Ontario however open to Toronto, Ontario as well.

The project requires a vision for client-side front-end development, focusing on how it should be architected. This vision must consider modern design patterns to ensure security, performance, scalability, and development efficiency to meet business needs. While Angular is the current enterprise standard, other technologies like WebLogic, JSP, JSF, WordPress, and PHP must also be factored into the business requirements and technology roadmaps for older applications. Collaboration with the business for discovery and IT reviews is essential.

SKILLS AND EXPERIENCES:

Core Angular Skills
Angular Framework: Deep understanding of Agular’s architecture, including components, services, directives, modules, and dependency injection. Proficiency with Angular CLI, RxJS, and state management patterns
Front-End Technologies
HTML5 and CSS3: Mastery of HTML5 and CSS3 for building user interfaces. Knowledge of CSS preprocessors like SASS or LESS can be beneficial
JavaScript: A strong grasp of JavaScript, including ES6+ features, is essential for modern web development
Development Tools and Practices
Version Control Systems: Proficiency in using Git for version control and collaboration.
Legacy Technologies
Java EE and WebLogic: Understanding of Java EE technologies and experience with WebLogic server for managing enterprise applications.
JSP/JSF and Prime Faces: Proficiency in Java Server Pages (JSP), Java Server Faces (JSF), and Prime Faces for building and maintaining older web applications.
Architectural Skills
Microservices Architecture: Understanding of microservices architecture and how to integrate Angular applications with microservices.
Responsive Additional Skills
Design: Understanding responsive web design principles to ensure applications work well on various devices and screen sizes1.
Testing Soft Skills
: Knowledge of testing frameworks and tools like Jasmine, Karma, and Protractor for unit and end-to-end testing.
Build Tools: Experience with build tools like Webpack or Angular CLI for optimizing and bundling code.
Problem-Solving: Strong analytical and problem-solving skills to debug and optimize applications.
Collaboration: Ability to work effectively in a team, communicate clearly, and collaborate with other developers and stakeholders.
Leadership: Ability to guide and mentor development teams, ensuring best practices and high-quality code.

Found ""Clearance"" or ""Polygraph"". Skipping this job!
",Skipped,Not Available
4012337464,https://www.linkedin.com/jobs/view/4012337464,Pending,2024-08-31 10:47:19.421695,2024-09-05 10:47:19.476009,Required experience is high,"
About the job
Are you a seasoned Cloud Infrastructure Engineer with a passion for building scalable, cutting-edge solutions? Our client is looking for a Senior/Lead Cloud Infrastructure Engineer to facilitate the development of innovative, efficient, and robust cloud architectures. f you have a strong background in AWS services, infrastructure-as-code, and automation, let's connect! Apply now or email your <NAME_EMAIL> to get in touch with our team. 

Key Responsibilities:
Architect and maintain scalable infrastructure solutions using AWS services like EC2, S3, RDS, Lambda, and VPC
Develop and manage infrastructure using Terraform, CloudFormation, or similar IaC tools to streamline and automate infrastructure management
Automate deployment, monitoring, and management processes to enhance efficiency and system reliability
Partner with software development teams to ensure smooth integration and deployment of applications
Create and maintain automation scripts in Python, Bash, or other scripting languages
Mentor and guide junior engineers, fostering a collaborative environment and sharing best practices

Qualifications:
Minimum of 6 years of experience with infrastructure-as-code tools to build and manage cloud infrastructure
At least 6 years of experience with AWS services and tools, including storage, compute, and container services
Bachelor’s degree in Computer Science, Engineering, or a related field, or equivalent work experience
Strong proficiency in Terraform, Ansible, Kubernetes, and AWS services
Expertise in scripting languages such as Python, Bash, or similar
Excellent analytical and problem-solving skills, with the ability to work independently and as part of a team

Salary Rate: $140-180k/year

Please note that the posted salary range for this role may vary based on seniority, qualifications, or prior experience. We are always looking for talented people to join our network, and if your desired compensation isn't reflected in this posting, please send your application for review regarding related opportunities.

Experience required 6 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4015112764,https://www.linkedin.com/jobs/view/4015112764,Pending,2024-09-04 15:47:43.013398,2024-09-05 10:47:43.137290,Required experience is high,"
About the job
Job Description : 

This position is for a Cloud Data engineer with a background in Python, Pyspark, SQL and data warehousing for enterprise level systems. The position calls for someone that is comfortable working with business users along with business analyst expertise.

Major Responsibilities: 

Build and optimize data pipelines for efficient data ingestion, transformation and loading from various sources while ensuring data quality and integrity.
Design, develop, and deploy Spark program in databricks environment to process and analyze large volumes of data.
Experience of Delta Lake, DWH, Data Integration, Cloud, Design and Data Modelling.
Proficient in developing programs in Python and SQL
Experience with Data warehouse Dimensional data modeling.
Working with event based/streaming technologies to ingest and process data.
Working with structured, semi structured and unstructured data.
Optimize Databricks jobs for performance and scalability to handle big data workloads. 
Monitor and troubleshoot Databricks jobs, identify and resolve issues or bottlenecks. 
Implement best practices for data management, security, and governance within the Databricks environment. Experience designing and developing Enterprise Data Warehouse solutions.
Proficient writing SQL queries and programming including stored procedures and reverse engineering existing process.
Perform code reviews to ensure fit to requirements, optimal execution patterns and adherence to established standards.

Skills: 

5+ years Python coding experience.
5+ years - SQL Server based development of large datasets
5+ years with Experience with developing and deploying ETL pipelines using Databricks Pyspark.
Experience in any cloud data warehouse like Synapse, Big Query, Redshift, Snowflake.
Experience in Data warehousing - OLTP, OLAP, Dimensions, Facts, and Data modeling.
Previous experience leading an enterprise-wide Cloud Data Platform migration with strong architectural and design skills.
Experience with Cloud based data architectures, messaging, and analytics.
Cloud certification(s).
Any experience with Airflow is a Plus.
 
Education : 

• Minimally a BA degree within an engineering and/or computer science discipline
• Master’s degree strongly preferred

We can offer you:

A highly competitive compensation and benefits package
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
Laptop and a mobile phone
15 days of paid annual leave (plus national holidays)
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability
RRSP with employer’s contribution
A higher education certification policy
Comprehensive Relocation Expense Coverage
Commuter benefits
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more. All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Experience required 5 > Current Experience 3. Skipping this job!
",Skipped,Not Available
4009287170,https://www.linkedin.com/jobs/view/4009287170,Pending,2024-09-04 10:47:47.986324,2024-09-05 10:47:48.027499,Required experience is high,"
About the job
Required:
- 3 years+ of build and platform experience 
- Experience working on build systems 
- Experience with cross compiler systems and/or compiler experience 
- Experience with CMake 
- Experience with Shell Scripting

Nice to have:
- Experience with Jenkins, Docker or containerization 
- Experience with Bitbake build engine and OpenEmbedded build system to build Linux distributions using Yocto

Insight Global is looking to bring on a Build & Platform Engineer for a large automotive company located in Kanata, ON. You will be responsible for configuring and integrating builds. You should have experience working in various platforms and be comfortable with cross compiler systems e.g. Linux/Unix, QNX, RTOS, etc. You should have strong troubleshooting experience and be comfortable with scripting. Any experience with Jenkins, Docker and containerization is an asset.

Experience required 3 > Current Experience 1. Skipping this job!
",Skipped,Not Available
3988767582,https://www.linkedin.com/jobs/view/3988767582,Pending,2024-08-29 10:47:51.062189,2024-09-05 10:47:51.132497,Required experience is high,"
About the job
Responsibilities:
Develop, test and maintain high-quality software using Python programming language.
Participate in the entire software development lifecycle, building, testing and delivering high-quality solutions.
Collaborate with cross-functional teams to identify and solve complex problems.
Write clean and reusable code that can be easily maintained and scaled.


Requirements:
7+ years of experience as a Python Developer with a strong portfolio of projects.
Bachelor's degree in Computer Science, Software Engineering or a related field.
In-depth understanding of the Python software development stacks, ecosystems, frameworks and tools such as Numpy, Scipy, Pandas, Dask, spaCy, NLTK, sci-kit-learn and PyTorch.
Experience with front-end development using HTML, CSS, and JavaScript.
Familiarity with database technologies such as SQL and NoSQL.
Must possess good experience in UNIX and Shell Scripting.
Excellent problem-solving ability with solid communication and collaboration skills.


It would be great if you also had:
Experience in banking domain.

We can offer you:
A highly competitive compensation and benefits package
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
15 days of paid annual leave (plus national holidays)
Maternity & Paternity leave plans
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability
RRSP with employer’s contribution
A higher education certification policy
Comprehensive Relocation Expense Coverage
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Experience required 7 > Current Experience 1. Skipping this job!
",Skipped,Not Available
3999334501,https://www.linkedin.com/jobs/view/3999334501,Pending,2024-08-22 10:48:10.140802,2024-09-05 10:48:10.240295,Required experience is high,"
About the job
Applied Systems, Inc., a worldwide leader in insurance technology, is currently searching for a Software Engineer (Golang, ETL, GCP) to join our Data Lake team. This role involves developing and maintaining robust data pipelines and contributing to our extensive data infrastructure.

As an ETL Software Engineer, you will collaborate with Architects, Product Owners, and Scrum team members to deliver high-quality technical solutions. You'll play a pivotal role in transforming and orchestrating massive data sets, creating impactful solutions that drive innovation and efficiency for our clients' businesses.

Design and deliver high-quality solutions in collaboration with the team,
Participate in design discussions to align your work with the technical vision,
Continuously improve the quality and efficiency of your development processes,
Engage the team for support with issues or blockers to maintain momentum
Request support with complex technical problems to ensure timely resolution

What You’ll Need to Succeed

3 - 5 years of software engineering experience, with a focus on ETL and data engineering
Proficiency in Golang and, Python, SQL
Experience with GCP data and ETL tools (e.g., Debezium, Pub/Sub, Dataflow, BigQuery) 
Cloud platform: GCP experience (six months - one year)
Experience in implementing RESTful services and APIs.
Knowledge of Agile frameworks, ideally Scrum, and tools like Jira and Confluence
Ability to communicate in a team-oriented environment to clarify requirements
Experience delivering solutions within committed timelines.
Proven technical abilities, troubleshooting skills, and research abilities.
Bachelor-level degree in Computer Science, MIS, or CIS, or equivalent experience
We proudly support and encourage people with military experience, as well as military spouses, to apply.

What You’ll Gain

Benefits from Day One
Health insurance plans, dental, and vision
Wellness incentives
401(k) and/or RRSP retirement savings plans with employer match
Work-Life Balance
Competitive paid vacation time and a free day for your birthday
Personal/sick time
Paid holidays
Flex Time
Paid parental leave (U.S. candidates)
Volunteer time off
Empowering Career Growth and Success – We invest in talent, care about our people and are empowered by the results of our work. We grow our teams from within and give our employees opportunities to advance.

What We Value
We strive for excellence at every turn to be the best at what we do. We invest in talent, care about our people and are empowered by the results of our work. We fulfil the promise of insurance – safeguarding and protecting what matters most in people’s lives. And there is no more important job than that.

Our focus on the workforce, workplace and marketplace gives us a qualified individual in an environment in which they can be productive while we maintain our position in the industry. To help drive that change toward a vibrant, modern workplace, we have employee-driven networks with commonalities in ethnicity, gender, sexual orientation and military status.

Who We Are

Applied Systems is the leading global provider of cloud-based software that powers the business of insurance. Recognized as a pioneer in insurance automation and the innovation leader, Applied is the world’s largest agency and brokerage management systems provider, serving customers throughout the United States, Canada, the Republic of Ireland, and the United Kingdom. By automating the insurance lifecycle, Applied’s people and products enable millions of people worldwide to safeguard and protect what matters most.

For 40 years, Applied Systems has led an industry we helped to create with a mission to continuously improve the business of insurance. From partnerships, acquisitions, and insurance innovation initiatives, Applied has focused on efforts to be an indispensable partner in our industry.

It’s an exciting time at Applied. You can do big things here, in an environment that supports creative thinking and bold ideas. Visit http://www.AppliedSystems.com for more information on how you can challenge what’s possible.

EEO Statement
Applied Systems is proud to be an Equal Employment Opportunity and Affirmative Action Employer. Diversity and Inclusion is a business imperative and is a part of building our brand and reputation. At Applied, we don’t discriminate, and we are committed to recruit, develop, retain, and promote regardless of race, religion, color, national origin, sexual orientation, gender identity, disability, age, veteran status, and other protected status as required by applicable law.

#LI-Remote

Experience required 3 > Current Experience 1. Skipping this job!
",Skipped,Not Available
3945334223,https://www.linkedin.com/jobs/view/3945334223,Pending,2024-08-15 10:49:11.547188,2024-09-05 10:49:11.641052,Required experience is high,"
About the job
Role: Java Developer with Spark
Location: Mississauga, ON (hybrid/3 days a week from day one)

Overview:
We are looking for a Java Spark Developer with experience in building high-performing, scalable, enterprise-grade applications.

The Role
Responsibilities:
Contribute in all phases of the development lifecycle
Write well designed, testable, efficient code
Ensure designs are in compliance with specifications
Prepare and produce releases of software components
Support continuous improvement by investigating alternatives and technologies and presenting these for architectural review

Requirements:
You are:
At least 8 years experience in designing and developing solutions using Core Java (Java/J2EE/Spring)
Experience with Web Services Security, REST, SOAP
Experience with Hadoop, Spark, Micro-Services, Apache-BEAM
Experience of Cloud development experience using Azure
Experience with Scala is a plus
Experience of SDLC experience on Java and Cloud Technology with overall cloud Architecture understanding
Hands-on experience with CI/CD pipeline Jenkins/Docker/OpenShift deployment
Strong working experience in Database

Education:
Bachelor's degree in Finance, Business, or a related field.

We can offer you:
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
15 days (3 weeks) of paid annual leave plus an additional 10 days of personal leave (floating days and sick days)
A comprehensive insurance plan including medical, dental, vision, life insurance, and long-term disability
Flexible hybrid policy to fit your schedule
RRSP with employer’s contribution up to 4%
A higher education certification policy
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A truly diverse, fun-loving and global work culture 

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Experience required 8 > Current Experience 1. Skipping this job!
",Skipped,Not Available
4002100053,https://www.linkedin.com/jobs/view/4002100053,Pending,2024-08-22 10:49:16.337328,2024-09-05 10:49:16.416899,Required experience is high,"
About the job
Position: On-prem Senior Application DevOps Engineer
Type: Fulltime
Location: Toronto
Environment: Hybrid (3 days onsite)
Salary: $120-140k depending on level of experience
 We are a successful Canadian manufacturer and distributor of high quality products sold globally. With 1,000's of employees we continue to grow and continue on our modernization journey. We have an exiting technology environment consisting of ERP systems, multiple SaaS platforms and internal customized applications, websites and ecommerce platforms.
 Due to our continued success and growth, we are adding a new Senior DevOps Engineer to the team.
 DevOps (primarily on-prem)
Design and implement end to end highly scalable and resilient solutions for infrastructure and application services with SaaS application hybrid clouds
Automate the software development lifecycle by Implementing CI /CD capabilities for infrastructure and applications in accordance with the established governance rules relating to code quality, test requirements, cybersecurity
Implement and manage Web Application Firewalls to provide unified application security posture protecting APIs & Web Applications at the edge; reduces client-side risks
Identifying and deploying cybersecurity measures by continuously performing vulnerability assessment and risk management. Address Common Vulnerabilities and Exposures (CVE) as per established procedures
Design, implement and manage Source Code Control (Github); binaries and artifacts via an Artifact Repository (JFrog or equivalent)
Design and implement Dev, QA / UAT and Production application environments
Ensure application environments, tools & approved 3rd party components are kept up to date as per established patching & update procedures.
Implement automated processes wherever possible with continuous modernization and upgrade of existing processes / scripts
Incidence management and root cause analysis
 Design and architecture
Drives continuous technology transformation to minimize technical debt
Work closely with cross-functional application & infrastructure teams to produce comprehensive end-to-end solution opportunities
Provides architecture direction for developers recognizing custom and standard technical frameworks, GRC (Governance, Risk & Compliance) audit policies and procedures
Participates in defining target state technology architecture and roadmaps & ensure alignment of initiatives with the target state
Builds strong partnership with developers to ensure proper alignment of skills with technology direction
Participate in code reviews, security audits, and performance testing to maintain the integrity of Cloud and Hybrid solutions
Stay up-to-date with the latest technologies and security trends to ensure our solutions remain innovative, secure, and cost-efficient
Define and maintain documentation of architectural solutions and procedures (Standard Operating Procedures)

Education & Experience
10+ years of relevant work experience
 Bachelor’s degree in information technology, software engineering, computer science, or related field.
 Proven experience in engineering and software architecture design.
 Sound knowledge of various operating systems and databases.
 Must be self-motivated and driven. Strong ability to work with internal resources and vendors

Must have technologies 
 Strong experience in RHEL Linux 
 Solid understanding of SDLC
 Strong experience in Ansible, Github, Jenkins, JFrog, Cloudflare Web Application Firewall, Docker / Podman / Kubernetes / Okta
 Experience with monitoring tools such as Prometheus, Grafana, or ELK stack.
 Excellent knowledge of critical concepts in DevOps and Agile principles
 Shell Scripting (Bash, Ruby, or Python, etc)
 Proficient in current web standards & frameworks

Experience required 10 > Current Experience 1. Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2024-08-15 10:49:21.211114,2024-09-05 10:49:21.278922,Required experience is high,"
About the job
Role Title: Senior DevOps Engineering Specialist
Location: Remote
Domian: Health/ Life Science
Any specific tools/skillset:
5+ years of multi-disciplinary experience in a large, complex organization
Bachelors’ degree in Computer Science and/or Software Engineering or equivalent experience
Ability to build and maintain collaborative stakeholder relationships across multiple geographies and business functions 
Ability to effectively communicate high-level concepts and solutions with technology and business teams 
You value simplicity and are unafraid to challenge technical constraints to utilize an iterative and agile approach 
Strong knowledge of infrastructure solutions, specifically in relation to cloud programs, platform migration, system security, enterprise directories, and cloud technologies
Kubernetes Expertise: Design, deploy, and maintain scalable and secure Kubernetes clusters, ensuring high availability, fault tolerance, and optimal performance for containerized applications
Experience working with technologies such as Docker, Kubernetes, Terraform, Ansible or other Infrastructure as Code (IaC) tools
Extensive experience with AWS EKS and Service Mesh
Expertise with CI/CD tooling such as Git, Jenkins, SonarQube, Nexus, Vault etc..
Experience working with at least one of the major public cloud providers (Azure, GCP or AWS) and a willingness to continue to expand your knowledge
A self-starter with the ability to comfortably operate in ambiguity
Monitoring and Troubleshooting: Monitor system performance, proactively identify issues, and implement measures for improvement to ensure system reliability and stability

Role profile description:
Reporting to the Product Owner, the DevOps Engineer is responsible for the monitoring, maintenance, and support of systems, services and tools, as well as deploying net-new capabilities aligned to DevOps principles. The DevOps Engineer will leverage technical knowledge and experience across a variety of business units including, infrastructure, development, operations, and quality assurance, ensuring emerging trends and new technologies stay top of mind. 
Ensure all solutions meet security and risk standards and comply with regulatory requirements as documented 
Ensure all functional and non-functional requirements are met and measurable in all environments 
Execute on the DevOps framework as defined by the Principal DevOps Engineer 
Proactively identify areas of friction in current deployment processes and drive technical solution to remove impediments 
Migrate existing services to new and existing digital stacks, while managing solutions by monitoring systems and events to avoid costly downtime 
Ensure technology capabilities align with current and future business needs 
Ensure alignment and adherence to enterprise technology, vendor management, and risk standards
Respond to critical and non-critical incidents based on priority and level of complexity 
Work to validate and triage issues, and resolve where applicable 
Manage ticket process from start to finish, interacting with other teams as necessary to identify and implement potential solutions

Experience required 5 > Current Experience 1. Skipping this job!
",Skipped,Not Available
