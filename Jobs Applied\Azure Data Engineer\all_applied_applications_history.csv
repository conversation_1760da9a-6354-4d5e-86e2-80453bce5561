Job ID,Title,Company,Work Location,Work Style,About Job,Experience required,Skills required,HR Name,HR Link,Resume,Re-posted,Date Posted,Date Applied,Job Link,External Job link,Questions Found,Connect Request
3996687464,"Senior Power BI Developer
Senior Power BI Developer with verification",Synechron,"Montreal, QC",Hybrid,"About the job
Position : Senior BI Developer
Location : Montreal, QC
Experience : 6 plus years
Type : Hybrid (2-3 days from office)

Job Overview: We are seeking a highly skilled and experienced BI Developer to join our dynamic team at our Montreal office. The ideal candidate will have a robust background in Business Intelligence, a deep understanding of SQL, and hands-on experience with Data and Semantic Modeling ,and YAML with Java-script . In addition to technical expertise, familiarity with Power BI is essential for success in this role. If you have a passion for transforming data into actionable insights and thrive in a fast-paced environment, we encourage you to apply.

Key Responsibilities:
Design, develop, and maintain scalable and reliable BI solutions to meet business requirements.
Craft and execute complex SQL queries for data analysis and reporting purposes.
Develop and optimize data models to improve data analytics and reporting capabilities.
Implement semantic models to facilitate intuitive data exploration for business users.
Collaborate with stakeholders to translate business needs into technical specifications.
Utilize Power BI for creating dashboards, visualizations, and reports that provide meaningful business insights.
Maintain documentation for BI solutions and provide training and support to end-users.
Stay abreast of BI trends and technologies and recommend improvements to existing systems and practices.
Qualifications:
Proven experience as a BI Developer or similar role in the financial services industry.
Strong command of SQL and experience in developing complex queries and stored procedures.
Expertise in Data Modeling and Semantic Modeling techniques.
Good Hands on experience with YAML and Java-Script.
Proficiency in Power BI for developing reports and dashboards.
Excellent problem-solving skills and attention to detail.
Strong communication and collaboration skills, with the ability to interact effectively with different business units.
Good to Have:
Experience with modern BI tools such as Cube Dev, AtScale, or Kyvos.
Familiarity with cloud services and data warehouse technologies.
Experience with agile methodologies and business intelligence best practices.
Education:
Bachelor’s degree in Computer Science, Information Technology, Data Analytics, or a related field.

Valid Canada Work Authorization is needed.

We can offer you:
A highly competitive compensation and benefits package.
A multinational organization with 55 offices in 20 countries and the possibility to work abroad
Laptop and a mobile phone.
15 days of paid annual leave (plus sick leave and national holidays)
A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability (plans vary by region)
Retirement savings plans
A higher education certification policy
Extensive training opportunities, focused on skills, substantive knowledge, and personal development
On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
A flat and approachable organization
A truly diverse, fun-loving and global work culture

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Same Difference’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.",0,In Development,"Omar Abbasi 
Omar Abbasi is verified",https://www.linkedin.com/in/omar-abbasi1,GIRISH SAI THIRUVIDHULA_AzureDE.pdf,False,2024-08-15 10:40:06.082191,2024-09-05 10:40:16.071187,https://www.linkedin.com/jobs/view/3996687464,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with extract, transform, load (etl)?', '4', 'text', '4'), ('how many years of work experience do you have with microsoft power bi?', '7', 'text', ''), ('how many years of financial services experience do you currently have?', '7', 'text', ''), ('how many years of information technology experience do you currently have?', '7', 'text', ''), ('how many years of work experience do you have with yaml?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
**********,"Big Data Developer
Big Data Developer with verification",LTIMindtree,"Mississauga, ON",On-site,"About the job
About Us:
LTIMindtree is a global technology consulting and digital solutions company that enables enterprises across industries to reimagine business models, accelerate innovation, and maximize growth by harnessing digital technologies. As a digital transformation partner to more than 700+ clients, LTIMindtree brings extensive domain and technology expertise to help drive superior competitive differentiation, customer experiences, and business outcomes in a converging world. Powered by nearly 90,000 talented and entrepreneurial professionals across more than 30 countries, LTIMindtree a Larsen & Toubro Group company combines the industry-acclaimed strengths of erstwhile Larsen and Toubro Infotech and Mindtree in solving the most complex business challenges and delivering transformation at scale. For more information, please visit .
Job Title: Big Data Developer
Work Location: Mississauga,Canada
Job Description:
Skill Big Data Spark SQL Scala
Mandotory Certification any one
Cloudera CCA Spark and Hadoop Developer CCA175
Databricks Certified Developer Apache Spark 2X
Hortonworks HDP Certified Apache Spark Developer
Job Description
Experience in Scala programming languages
Experience in Big Data technologies including Spark Scala and Kafka
You have a good understanding of organizational strategy architecture patterns Microservices Event Driven and technology choices and coaching the team in execution in alignment to these guidelines
You can apply organizational technology patterns effectively in projects and make recommendations on alternate options
You have handson experience working with large volumes of data including different patterns of data ingestion processing batch realtime movement storage and access for both internal and external to BU and ability to make independent decisions within scope of project
You have a good understanding of data structures and algorithms
You can test debug and fix issues within established SLAs
You can design software that is easily testable and observable
You understand how teams goals fit a business need
You can identify business problems at the project level and provide solutions
You understand data access patterns streaming technology data validation data performance cost optimization
Strong SQL skills
ETL TalenD preferred Any other ETL tool
Experience with Linux OS user level
Python or R programming skills good to have but not manditory
Benefits/perks listed below may vary depending on the nature of your employment with LTIMindtree ( LTIM ):
Benefits and Perks:
Comprehensive Medical Plan Covering Medical, Dental, Vision
Short Term and Long-Term Disability Coverage
401(k) Plan with Company match
Life Insurance
Vacation Time, Sick Leave, Paid Holidays
Paid Paternity and Maternity Leave
The range displayed on each job posting reflects the minimum and maximum salary target for the position across all US locations. Within the range, individual pay is determined by work location and job level and additional factors including job-related skills, experience, and relevant education or training. Depending on the position offered, other forms of compensation may be provided as part of overall compensation like an annual performance-based bonus, sales incentive pay and other forms of bonus or variable compensation.
Disclaimer: The compensation and benefits information provided herein is accurate as of the date of this posting.",0,In Development,"Chakradhar M 
Chakradhar M is verified",https://www.linkedin.com/in/chakradhar-chakri-76b206188,Previous resume,False,2024-09-03 10:40:26.390449,2024-09-05 10:40:29.166915,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with sql?', '3', 'text', '3'), ('how many years of work experience do you have with scala?', '2', 'text', '2'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('Do you have a valid driver\'s license? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None)}",In Development
**********,"Power BI Developer
Power BI Developer with verification",Synechron,"Montreal, QC",Hybrid,"About the job
We are
At Synechron, we believe in the power of digital to transform businesses for the better. Our global consulting firm combines creativity and innovative technology to deliver industry-leading digital solutions. Synechron’s progressive technologies and optimization strategies span end-to-end Artificial Intelligence, Consulting, Digital, Cloud & DevOps, Data, and Software Engineering, servicing an array of noteworthy financial services and technology firms. Through research and development initiatives in our FinLabs we develop solutions for modernization, from Artificial Intelligence and Blockchain to Data Science models, Digital Underwriting, mobile-first applications and more. Over the last 20+ years, our company has been honored with multiple employer awards, recognizing our commitment to our talented teams. We began life in 2001 as a small, self-funded team of technology specialists. Since then, we’ve grown our organization to over 14,000+ people, across 55 offices, in 20 countries, in key global markets.

Job Title: Power BI Developer
Job Locations: Montreal, CN

Our challenge
As a Power BI developer, your primary role will be to deliver business intelligence services, lead BI software development, and present Power BI reports. You will transform raw data into cohesive, valuable reports capturing meaningful business insights.

The Role
Responsibilities:
Designing and developing Power BI reports and dashboards to meet the business stakeholders’ needs
Gathering and understanding business requirements for data visualization and analysis
Collaborating with data engineers and analysts to acquire, clean, and transform data for reporting purposes
Creating complex DAX calculations and measures to support data analysis
Ensuring data security and compliance with best practices
Troubleshooting and resolving issues in Power BI reports
Providing training and support to end users on using Power BI
Keeping up-to-date with the latest Power BI features and trends

Requirements: 
You are: 
Candidates should have strong BI background
Strong SQL Skills – Must Have
Experience in Data Modeling, Semantic Modeling
Should have financial service background
Knowledge of Power BI is must
Good to Have: Experience in tools like Cube Dev, AtScale, Kyvos

It would be great if you also had:
Experience in tools like Cube Dev, AtScale, Kyvos

We can offer you:
· A highly competitive compensation and benefits package
· A multinational organization with 55 offices in 20 countries and the possibility to work abroad
· Laptop and a mobile phone
· 15 days of paid annual leave (plus sick leave and national holidays)
· A comprehensive insurance plan including: medical, dental, vision, life insurance, and long-/short-term disability (plans vary by region)
· Retirement savings plans
· A higher education certification policy
· Extensive training opportunities, focused on skills, substantive knowledge, and personal development
· On-demand Udemy for Business for all Synechron employees with free access to more than 5000 curated courses
· Coaching opportunities with experienced colleagues from our Financial Innovation Labs (FinLabs) and Center of Excellences (CoE) groups
· Cutting edge projects at the world’s leading tier-one banks, financial institutions and insurance firms
· A flat and approachable organization
· A truly diverse, fun-loving and global work culture

SYNECHRON’S DIVERSITY & INCLUSION STATEMENT
Diversity & Inclusion are fundamental to our culture, and Synechron is proud to be an equal opportunity workplace and is an affirmative action employer. Our Diversity, Equity, and Inclusion (DEI) initiative ‘Synclusive’ is committed to fostering an inclusive culture – promoting equality, diversity and an environment that is respectful to all. We strongly believe that a diverse workforce helps build stronger, successful businesses as a global company. We encourage applicants from across diverse backgrounds, race, ethnicities, religion, age, marital status, gender, sexual orientations, or disabilities to apply. We empower our global workforce by offering flexible workplace arrangements, mentoring, internal mobility, learning and development programs, and more.
All employment decisions at Synechron are based on business needs, job requirements and individual qualifications, without regard to the applicant’s gender, gender identity, sexual orientation, race, ethnicity, disabled or veteran status, or any other characteristic protected by law.

Nous sommes Chez Synechron, nous croyons au pouvoir du numérique pour transformer les entreprises en mieux. Notre cabinet de conseil mondial allie créativité et technologie innovante pour offrir des solutions numériques de premier plan. Les technologies progressives et les stratégies d'optimisation de Synechron couvrent l'intelligence artificielle, le conseil, le numérique, le cloud & DevOps, les données et le génie logiciel de bout en bout, au service d'un éventail d'entreprises de services financiers et de technologie remarquables. Grâce à des initiatives de recherche et développement dans nos FinLabs, nous développons des solutions de modernisation, de l'intelligence artificielle et la blockchain aux modèles de science des données, à la souscription numérique, aux applications mobiles et plus encore. Au cours des 20 dernières années, notre entreprise a été honorée par de multiples prix d'employeur, reconnaissant notre engagement envers nos équipes talentueuses. Nous avons commencé en 2001 en tant qu'une petite équipe autonome de spécialistes de la technologie. Depuis lors, nous avons développé notre organisation pour compter plus de 14 000 personnes, réparties dans 55 bureaux, dans 20 pays, sur les principaux marchés mondiaux.
Titre du poste : Développeur Power BI Lieu : Montréal, CN
Notre défi En tant que développeur Power BI, votre rôle principal sera de fournir des services d'intelligence d'affaires, de diriger le développement de logiciels BI et de présenter des rapports Power BI. Vous transformerez les données brutes en rapports cohérents et précieux capturant des informations commerciales significatives.
Le rôle Responsabilités : Concevoir et développer des rapports et des tableaux de bord Power BI pour répondre aux besoins des parties prenantes commerciales Recueillir et comprendre les exigences commerciales en matière de visualisation et d'analyse des données Collaborer avec les ingénieurs et les analystes de données pour acquérir, nettoyer et transformer les données à des fins de reporting Créer des calculs et des mesures DAX complexes pour soutenir l'analyse des données Garantir la sécurité des données et la conformité aux meilleures pratiques Résoudre les problèmes dans les rapports Power BI Fournir une formation et un soutien aux utilisateurs finaux sur l'utilisation de Power BI Rester à jour avec les dernières fonctionnalités et tendances Power BI
Exigences : Vous êtes : Les candidats devraient avoir une solide expérience en intelligence d'affaires Solides compétences en SQL – Obligatoire Expérience en modélisation de données, modélisation sémantique Devrait avoir une expérience dans le secteur des services financiers Connaissance de Power BI est indispensable
Ce serait formidable si vous aviez également : Expérience dans des outils comme Cube Dev, AtScale, Kyvos
Nous pouvons vous offrir : · Un package de rémunération et d'avantages sociaux hautement compétitif · Une organisation multinationale avec 55 bureaux dans 20 pays et la possibilité de travailler à l'étranger · Un ordinateur portable et un téléphone portable · 15 jours de congé annuel payé (plus congé maladie et jours fériés nationaux) · Un régime d'assurance complet comprenant : médical, dentaire, vision, assurance vie et invalidité à long/à court terme (les plans varient selon la région) · Des régimes d'épargne-retraite · Une politique de certification de l'enseignement supérieur · De nombreuses opportunités de formation axées sur les compétences, les connaissances substantielles et le développement personnel · Udemy à la demande pour les entreprises pour tous les employés de Synechron avec un accès gratuit à plus de 5000 cours sélectionnés · Des opportunités de coaching avec des collègues expérimentés de nos Financial Innovation Labs (FinLabs) et de nos groupes Centre d'excellence (CoE) · Des projets de pointe auprès des principales banques de premier plan, des institutions financières et des sociétés d'assurance dans le monde · Une organisation accessible et à structure horizontale · Une culture de travail véritablement diversifiée, plaisante et mondiale
DÉCLARATION DE DIVERSITÉ ET D'INTÉGRATION DE SYNECHRON La diversité et l'inclusion sont fondamentales à notre culture, et Synechron est fier d'être un lieu de travail offrant l'égalité des chances et d'être un employeur pratiquant l'action positive. Notre initiative sur la diversité, l'équité et l'inclusion (DEI) « Synclusive » s'engage à favoriser une culture inclusive - promouvant l'égalité, la diversité et un environnement respectueux envers tous. Nous croyons fermement qu'une main-d'œuvre diversifiée contribue à renforcer les entreprises prospères en tant qu'entreprise mondiale. Nous encourageons les candidatures de personnes de divers horizons, races, ethnies, religions, âges, états civils, genres, orientations sexuelles ou handicaps. Nous autonomisons notre main-d'œuvre mondiale en offrant des aménagements de travail flexibles, du mentorat, de la mobilité interne, des programmes d'apprentissage et de développement, et plus encore. Toutes les décisions d'emploi chez Synechron sont basées sur les besoins de l'entreprise, les exigences du poste et les qualifications individuelles, sans tenir compte du genre, de l'identité de genre, de l'orientation sexuelle, de la race, de l'ethnie, du handicap ou du statut d'ancien combattant du candidat, ou de toute autre caractéristique protégée par la loi.",Error in extraction,In Development,"Taiyaba Ansari 
Taiyaba Ansari is verified",https://www.linkedin.com/in/taiyaba-ansari-b85203135,Previous resume,False,2024-08-22 10:40:45.608056,2024-09-05 10:40:48.419129,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of information technology experience do you currently have?', '7', 'text', '7'), ('how many years of work experience do you have with microsoft power bi?', '7', 'text', '7'), ('how many years of work experience do you have with javascript?', '3', 'text', '3'), ('how many years of work experience do you have with yaml?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
3997370883,"Java Spark Developer
Java Spark Developer with verification",VLink Inc,"Mississauga, ON",Hybrid,"About the job
Job Description:
 We are seeking a Java Spark Developer to join our team and contribute to the development, programming, and maintenance of applications using the Apache Spark open-source framework. The ideal candidate will have strong programming skills in Java, Scala, or Python and a deep understanding of the Spark ecosystem, including Spark SQL, DataFrames, Datasets, and streaming.


Requirements: 
  Senior Java resource with 8+ Experience here is the expectation (score from 1 to 10 (best)
Experience in Spring boot, Microservices.
Java 11 version, Spring boot, Spring framework, Microservices, basics around SQL, Database (No SQL preferred), Message platform (like Kafka / JMS / MQ equivalent).
Core Java 
Database ( Basic understanding + SQL) 
Spark / Kafka / Micro Services / Spring boot",0,In Development,"Amit Kumar 
Amit Kumar is verified",https://www.linkedin.com/in/amit-kumar-598b547,Previous resume,False,2024-08-15 10:40:55.691259,2024-09-05 10:40:58.277243,https://www.linkedin.com/jobs/view/3997370883,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with java?', '1', 'text', '1'), ('how many years of work experience do you have with scala?', '2', 'text', '2'), ('how many years of work experience do you have with python (programming language)?', '3', 'text', '3'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4011677088,"Cloud Microservices Developer
Cloud Microservices Developer",EVONA,"Vancouver, BC",Hybrid,"About the job
Cloud Engineer - Hybrid - Vancover

Are you excited about working on cutting-edge cloud technologies for geotechnical monitoring systems?

A leading company in the field of IoT and geospatial monitoring solutions is looking for a skilled Senior Cloud Microservices Developer to join their innovative team in Vancouver, BC. This role offers a unique opportunity to contribute to the development of systems that have a real-world impact on safety, productivity, and environmental sustainability.

The Role

As a Senior Cloud Microservices Developer, you will be responsible for building and maintaining a cloud data store and API specifically designed for ground radar data utilized in geotechnical monitoring. Your work will ensure the secure and efficient storage of critical data, which is essential for supporting GIS applications used in monitoring the safety of infrastructure like dams, bridges, and tunnels.

What You’ll Do:
Develop and deploy data delivery systems for GIS applications with real-world impacts.
Collaborate with product managers and end users to understand and meet critical monitoring workflow requirements.
Maintain web infrastructure to ensure reliable, scalable performance.
Write clean, modular code adhering to best practices and coding standards.
Mentor junior developers and participate in technical discussions and code reviews.
Assist in project planning, defining project scope, and timelines.

What You’ll Need:

Educational Background: Bachelor's or Master's in Computer Science, Software Engineering, Information Technology, Geomatics, or a related field. A combination of education and relevant experience will be considered.

Technical Skills:
Experience with cloud-based architecture solutions and leading small technical teams.
Proficiency in RESTful APIs, Websockets, and OAuth.
Experience deploying cloud infrastructure using an Infrastructure as Code (IaC) language.
Familiarity with CI/CD processes and real-time apps (e.g., SignalR, Socket.IO).
Understanding of spatial data formats (GeoJSON, Shapefiles, GeoTIFFs) and spatial data visualization techniques.

What You’ll Get in Return:
Competitive Salary: CAD 105,000 - CAD 150,000 annually (based on experience and skills).
Flexible Work Schedule: Hybrid work model with 3 days in the Vancouver office and 2 days remote.
Global Opportunities: Be part of a worldwide company with diverse career paths and growth opportunities.
Supportive Culture: Learn from a talented team in a collaborative, safety-focused workplace.

Ready to make a difference in geotechnical monitoring?

Apply now or contact Ciara Holmes at Evona for more information.",0,In Development,"Ciara Holmes 
Ciara Holmes is verified",https://www.linkedin.com/in/ciaraholmesbsq,Previous resume,False,2024-08-30 10:41:02.840382,2024-09-05 10:41:06.327748,https://www.linkedin.com/jobs/view/4011677088,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('Are you legally authorized to work in Canada? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('how many years of work experience do you have with continuous integration and continuous delivery (ci/cd)?', '7', 'text', ''), ('Will you now or in the future require sponsorship for employment visa status? [  ""Yes""<Yes>, ""No""<No>, ]', 'No', 'radio', None), ('how many years of work experience do you have with rest apis?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
3996687313,"Machine Learning Engineer
Machine Learning Engineer",eTeam,"Montreal, QC",On-site,"About the job
Title: Machine Learning Enfgineer
Location: Montreal, QC
Duration: 09 Months
Pay rate: $90/hr to $100/hr without any benefits

Responsibilities: The Machine Learning (ML) Expert will:
• Understand the business needs and requirements and translate them into ML solutions.
• Communicate technical concepts and results to non-technical stakeholders in a clear and concise manner.
• Collaborate with internal clients to identify new opportunities for ML applications and models.
• Ensure that ML Models are aligned with the bank’s overall strategy and goals. • Work with Application Development teams to deploy and support ML models in production environments. 
• Stay up to date with latest development in ML and AI and apply this knowledge to first improve the performances of applications (current & past), and second to the benefits of the team. 

Minimum Required Qualifications
 • Master’s degree in computer science, mathematics, statistics, or a related field. 
• Strong programming skills in languages such as Python, database manipulation (SQL), statistical modeling, and data analysis techniques. 
• Experience with machine learning frameworks such as Tensorflow, PyTorch, or scikit-learn. 
• Experience in developing and working on real-world machine learning projects. 
• Strong problem-solving and analytical skills. 
• Excellent communication (both French and English) and collaboration skills. 
• Very strong work ethic and ability to deal with confidential information. 

Nice-to-have Qualifications 
• Experience in deploying models to production. 
• Experience with CI/CD pipelines (GIT, Jenkins, …) and MLOps (MLFlow). 
• Experience with containerization techniques (Docker & Kubernetes). 
• Experience with mentoring junior team members.",0,In Development,"Shaik Arbaz 
Shaik Arbaz is verified",https://www.linkedin.com/in/shaik-arbaz-87a658117,Previous resume,False,2024-08-15 10:41:10.714455,2024-09-05 10:41:14.187148,https://www.linkedin.com/jobs/view/3996687313,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('What is your level of proficiency in French? [  ""Select an option"", ""None"", ""Conversational"", ""Professional"", ""Native or bilingual"", ]', 'Professional', 'select', 'Select an option'), ('mobile phone number', '**********', 'text', '**********'), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('Are you comfortable commuting to this job\'s location? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4006865921,"OSS System Integrator
OSS System Integrator with verification",HCLTech,"Brampton, ON",On-site,"About the job
Role: System Integrator 
Job Location: Brampton, ON (Hybrid) 
Skills Needed:
Proven experience in OSS system management and integrations
Work in collaboration with vendors to manage system requirements, updates, and troubleshooting
Work as system integrator, sign off on Acceptance Testing ,documents ,security approval and oversee project on behalf of HCLTech and it's customer. 
knowledge of CI/CD pipelines & tools for continuous integration and deployment
Familiarity with EMS/controllers/NMS and FCAPS management
Experience with system integrations like inventory, Netcool, Syslog, etc.
understanding of Kubernetes / cloud native infrastructure like open shift , Tanzu etc.
Experience of working for Canadian Telcos like Rogers , Bell or TELUS will be added advantage.",0,In Development,Unknown,Unknown,Previous resume,False,2024-08-22 10:41:20.669884,2024-09-05 10:41:22.717811,https://www.linkedin.com/jobs/view/4006865921,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('mobile phone number', '**********', 'text', '**********')}",In Development
3996810163,"Machine Learning Engineer
Machine Learning Engineer with verification",US Tech Solutions,"Montreal, QC",Hybrid,"About the job
Duration: 09 months contract

Job Description:
The Machine Learning Engineer will be responsible for designing, implementing, and deploying machine learning models and algorithms to solve complex business problems. You will work closely with data scientists, software engineers, and product managers to develop scalable solutions that leverage advanced analytics and machine learning techniques.

Responsibilities:
The Machine Learning (ML) Expert will:
Understand the business needs and requirements and translate them into ML solutions.
Communicate technical concepts and results to non-technical stakeholders in a clear and concise manner.
Collaborate with internal clients to identify new opportunities for ML applications and models.
Ensure that ML Models are aligned with the bank’s overall strategy and goals.
Work with Application Development teams to deploy and support ML models in production environments.
Stay up to date with the latest development in ML and AI and apply this knowledge to first improve the performances of applications (current & past), and second to the benefits of the team.

Experience:
8+ of hands-on experience in designing, developing, and deploying machine learning models for real-world applications. Proven ability to work with various types of models such as classification, regression, clustering, and deep learning.

Skills:
Experience with machine learning frameworks such as Tensorflow, PyTorch, or scikit-learn. 
Strong programming skills in languages such as Python, database manipulation (SQL), statistical modeling, data analysis techniques.
Experience with CI/CD pipelines (GIT, Jenkins, …) and MLOps (MLFlow).

Education:
 Master’s degree in computer science, mathematics, statistics, or a related field. 

About US Tech Solutions:
US Tech Solutions is a global staff augmentation firm providing a wide range of talent on-demand and total workforce solutions. To know more about US Tech Solutions, please visit www.ustechsolutions.com.

US Tech Solutions is an Equal Opportunity Employer. All qualified applicants will receive consideration for employment without regard to race, color, religion, sex, sexual orientation, gender identity, national origin, disability, or status as a protected veteran.

Recruiter Details:
Name: Anisha
Email: <EMAIL>
Internal Id: 24-18627",0,In Development,Unknown,Unknown,Previous resume,False,2024-08-15 10:44:14.760109,2024-09-05 10:44:16.620707,https://www.linkedin.com/jobs/view/3996810163,Easy Applied,"{('phone', '**********', 'text', '**********'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>')}",In Development
**********,"AI Application Developer - HYBRID $70-85/hr (1655)
AI Application Developer - HYBRID $70-85/hr (1655)","Direct IT Recruiting Inc., WBE Canada Certified","Toronto, ON",Hybrid,"About the job
NOTE: HYBRID work model, 1 day/week in Toronto office

SKILLS: AI Developer, LLM, LLAMA, Python, SQL, Azure API, CI/CD, Kubernetes Microsoft PromptFlow, RAGS 
TYPE: 6-Month Contract, 7.5 hours/day, 37.5 hours/week
INDUSTRY: Banking

DESCRIPTION:

Proficient in Python and frameworks (Django, Flask, langchain, llama index, FastAPI)
Skilled in Python environments (PyCharm, Jupyter, Visual Studio Code)
Deploy Python applications on Azure (Azure App Services, Azure Functions)
Experience with Azure OpenAI Service
Experience with working with open source LLMs such as LLAMA and Mixtral
Experience building Gen AI applications.
Develop and integrate RESTful APIs, focusing on Azure API management
Deploy and manage Python apps on Azure, optimize cloud services
Experience with CI/CD pipelines (Azure DevOps, Jenkins, CircleCI, GitLab Actions)
Knowledge of containerization/orchestration (Docker, Kubernetes) in Azure
Experience with Microsoft PromptFlow or RAGS evaluation matrix
Experience working on Retrieval Augmented Generation framework
Expertise in writing and tune prompts for LLMs
Experience with indexing/chunking strategies for RAG applications
Work with search solutions (Azure AI search)
Advanced proficiency with Git
SQL

To Apply: https://directitrecruiting.com/job/ai-application-developer-hybrid/",0,In Development,"Parker Somerville 
Parker Somerville is verified",https://www.linkedin.com/in/parker-somerville,Previous resume,False,2024-09-01 10:44:21.074950,2024-09-05 10:44:24.833617,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of experience with ci/cd?', '7', 'text', ''), ('how many years of work experience do you have with azure api management?', '7', 'text', ''), ('how many years of work experience do you have with python (programming language)?', '3', 'text', '3'), ('Are you comfortable commuting to this job\'s location? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('how many years of work experience do you have with llama?', '7', 'text', ''), ('how many years of experience with t-sql?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('what is your hourly rate?', '7', 'text', '')}",In Development
4010158746,"Python Developer
Python Developer with verification",Genpact,"Montreal, QC",Hybrid,"About the job
Are you the One we are looking for?

Inviting applications for the role of Python Developer, at Montreal, QC, Fulltime

As a Python Developer, you will be a key member of the IT Applications Development team driving the quality of the solution and services meant to run the business. you will be responsible for designing and developing solutions for client business. You should have an end-to-end understanding of business use cases, to transform them into an effective and strategic solution. You will have the opportunity to work with the latest platform and applications on the given stack.
 
Responsibilities
Responsible for crafting clean, functional code that flawlessly suits the needs of the company. Your main focus will be translating the design concepts and requirements into simpler implementation level details & designing and developing Python applications.
Collaborate with product owners to create and define user stories and acceptance criteria.
Translate the design concepts, requirements into simpler implementation level details
Initiating collaboration with technical and product teams to identify development requirements and terms
Analysing the clients’ requirements and prioritising their suggested features
Writing well-structured, well-tested, clean quotes with Python programming language to create new applications or add features & improvements to existing services
Working in an Agile environment, following Scrum principles to break silos and support faster iteration and implementation of codes & apps.
Excellent written and verbal communication skills.
Excellent customer facing skills that include conducting compelling technical briefing & demonstrations.
Perform code reviews and check code coverage to ensure the modularity and quality of the code and application.
Work independently with limited or no handholding.
Responsible for troubleshooting issues found during development and providing necessary resolution.
Engage in technical discussions; participate in technical designs and present technical ideas through white board.
Seed and provide feedback on design and development.
Development of features and ALM integrations REST API use
Development test cases using the BDD framework.
Creating incremental SDLC-related functionality.
Preparation and deployment to production, deployment to go live, and postproduction support, as well as bug fixes and the correction of any new or existing issues.
 
Preferred Qualifications
Good Python experience (REST APIs/Flask)
Design and develop Microservices systems with Python.
AWS Compute, S3, API Gateway.
AWS Lambda based development.
Programming for backend databases like AWS DynamoDB or any other RDBMS or NoSQL DBs.
Front-end development utilizing Electron VUE
Experience with Elastic Search
Exposure to Continuous integration using DevOps - Jenkins and other CI/CD tools.
Exposure to Linux, Apache/httpd, Networking, Firewalls, security, etc.
Good Analytical, Problem-solving and design skills (HLD/LLD) skills
Fluent with object-oriented programming principles.
Familiarity with common stacks
Understanding of fundamental design principles behind a scalable application
Good Understanding of Agile Delivery Methodology & experience in working with Scrum teams



""Genpact is an Equal Opportunity Employer and considers applicants for all positions without regard to race, color, religion or belief, sex, age, national origin, citizenship status, marital status, military/veteran status, genetic information, sexual orientation, gender identity, physical or mental disability or any other characteristic protected by applicable laws. Genpact is committed to creating a dynamic work environment that values diversity and inclusion, respect and integrity, customer focus, and innovation. For more information, visit www.genpact.com . Follow us on Twitter, Facebook, LinkedIn, and YouTube.
 Furthermore, please do note that Genpact does not charge fees to process job applications and applicants are not required to pay to participate in our hiring process in any other way. Examples of such scams include purchasing a 'starter kit,' paying to apply, or purchasing equipment or training. """,0,In Development,Unknown,Unknown,Previous resume,False,2024-08-29 10:44:34.356742,2024-09-05 10:44:36.478264,https://www.linkedin.com/jobs/view/4010158746,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('mobile phone number', '**********', 'text', '**********')}",In Development
**********,"Data Architect
Data Architect with verification",LTIMindtree,"Mississauga, ON",On-site,"About the job
About Us:
LTIMindtree is a global technology consulting and digital solutions company that enables enterprises across industries to reimagine business models, accelerate innovation, and maximize growth by harnessing digital technologies. As a digital transformation partner to more than 700+ clients, LTIMindtree brings extensive domain and technology expertise to help drive superior competitive differentiation, customer experiences, and business outcomes in a converging world. Powered by nearly 90,000 talented and entrepreneurial professionals across more than 30 countries, LTIMindtree — a Larsen & Toubro Group company — combines the industry-acclaimed strengths of erstwhile Larsen and Toubro Infotech and Mindtree in solving the most complex business challenges and delivering transformation at scale. For more information, please visit www.ltimindtree.com.

Job Title: Data Architect

Work Location- Mississauga, Canada

Job Description:

Migrate data from legacy systems to new solutions
Design conceptual and logical data models and flowcharts
Improve system performance by conducting tests troubleshooting and integrating new elements
Optimize new and current database systems
Define security and backup procedures
Coordinate with the Data Science department to identify future needs and requirements
Provide operational support for Management Information Systems MIS
Proven work experience as a Data Architect Data Scientist Data Analyst or similar role
Indepth understanding of database structure principles
Experience gathering and analyzing system requirements
Knowledge of data mining and segmentation techniques
Expertise in SQL and Oracle Bigdata cloud AWS
Familiarity with data visualization tools eg Tableau D3js and R
Proven analytical skills
Problemsolving attitude

Certification Databricks certified associate developer for apache spark 30 Scala

Benefits/perks listed below may vary depending on the nature of your employment with LTIMindtree Financial Services Technologies Inc. (“LTIM FSTI”) (exclusive):

Benefits and Perks:
Comprehensive Medical Plan Covering Medical, Dental, Vision
Short Term and Long-Term Disability Coverage
Health Care Spending Account
Group RRSP Plan with Company match
Life Insurance
Annual Vacation and other Paid Leaves
Maternity Leave Top Up Pay

The range displayed on each job posting reflects the minimum and maximum salary target for the position across all Canada locations. Within the range, individual pay is determined by work location and job level and additional factors including job-related skills, experience, and relevant education or training. Depending on the position offered, other forms of compensation may be provided as part of overall compensation like an annual performance-based bonus, sales incentive pay and other forms of bonus or variable compensation.

Disclaimer: The compensation and benefits information provided herein is accurate as of the date of this posting.

LTIMindtree is an equal opportunity employer that is committed to diversity in the workplace. Our employment decisions are made without regard to race, color, creed, religion, sex (including pregnancy, childbirth or related medical conditions), gender identity or expression, national origin, ancestry, age, family-care status, veteran status, marital status, civil union status, domestic partnership status, military service, handicap or disability or history of handicap or disability, genetic information, atypical hereditary cellular or blood trait, union affiliation, affectional or sexual orientation or preference, or any other characteristic protected by applicable federal, state, or local law, except where such considerations are bona fide occupational qualifications permitted by law.

Safe return to office:
In order to comply with LTIMindtree’ s company COVID-19 vaccine mandate, candidates must be able to provide proof of full vaccination against COVID-19 before or by the date of hire. Alternatively, one may submit a request for reasonable accommodation from LTIMindtree’s COVID-19 vaccination mandate for approval, in accordance with applicable state and federal law, by the date of hire. Any request is subject to review through LTIMindtree’s applicable processes.",0,In Development,"Piyush Sharma ☮ 
Piyush Sharma ☮ is verified",https://www.linkedin.com/in/piyush-sharma-%E2%98%AE-91a458118,Previous resume,False,2024-08-30 10:45:10.799164,2024-09-05 10:45:13.498583,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Do you have a valid driver\'s license? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with amazon web services (aws)?', '1', 'text', '1'), ('how many years of work experience do you have with sql?', '3', 'text', '3'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
**********,"Solutions Architect
Solutions Architect with verification",LTIMindtree,"Markham, ON",Hybrid,"About the job
About Us:
LTIMindtree is a global technology consulting and digital solutions company that enables enterprises across industries to reimagine business models, accelerate innovation, and maximize growth by harnessing digital technologies. As a digital transformation partner to more than 700+ clients, LTIMindtree brings extensive domain and technology expertise to help drive superior competitive differentiation, customer experiences, and business outcomes in a converging world. Powered by nearly 90,000 talented and entrepreneurial professionals across more than 30 countries, LTIMindtree — a Larsen & Toubro Group company — combines the industry-acclaimed strengths of erstwhile Larsen and Toubro Infotech and Mindtree in solving the most complex business challenges and delivering transformation at scale. For more information, please visit www.ltimindtree.com.

Job Title: Solutions Architect

Work Location: Markham, ON, Canada (Hybrid – Onsite : 03 days a week)

Role Purpose: The purpose of the solution Architect role is to: 
Develop end to end solution architecture for the project meeting business requirements. 
Identify impact to the system for the proposed changes and work with various stakeholders in quantify and qualifying the impact 
Identify the technical debt – application and infrastructure in the landscape.
Create framework to evaluate and prioritize technical debt items. 
Develop roadmaps in conjunction with the Enterprise Architect and business to remediate technical debt. 
Support cost estimation and execution of the tech debt remediation 
Deliver solutions that progress the overall architecture journey and are aligned to Client’s IT Strategy.
Comply with standards, design approaches and governance.

Accountabilities: Nature of Work:
Solutions and Assurance:
Create solutions and design for projects to meet client’s standards for technology and ensure they meet our ambitions for availability, security, resilience, and performance
Produce design options with associated costs benefits and risks to enable investment decisions
Create migration solution to convert the policies from legacy system to Guidewire PolicyCenter
Work with product owners’ enterprise architect’s platform teams and other project colleagues to agree tradeoffs between long term and short-term goals
Responsible for the integrity of the end-to-end solution across Platforms including transition states used to achieve the target state
Shape and estimate IT initiatives and feature team sprints ensuring that component parts deliver against business outcomes
Ability to communicate complex technical concepts to both technical and nontechnical audiences
Record any solution debt incurred during the transition phase of the solution and address the same in future phases
Identify potential risks and challenges associated with proposed solutions
Develop mitigation strategies to address identified risks
Participate in postimplementation reviews and provide feedback to improve future solution designs
Technical Leadership:
Keep up with technological developments in the digital, integration area and evaluate how well they fit into the company’s architecture
Evaluate and recommend tools, frameworks, and platforms to support the development and deployment of solutions
Support defining the architecture best practices and standards and enforcing them in the project solutions
Conduct architecture reviews to ensure compliance with established guidelines
Ensure design artefacts are produced to support stakeholder group for service, security, architecture, and engineering
Peer reviews the design artefacts of others
Provide coaching mentoring and training to less experienced team members as required
Design Community membership:
Contribute to the Solution Architecture community across client Group leading on or participating in initiatives to improve approach to design
Develop your technical skills to support need to understand and exploit emerging technology trends Risk.
Risk & Controls:
Identify, own, and manage the specific key risks and/or IT controls and BP standards that you are identified as the owner and/or nominee for on iCARE or Archer 
Ensure that issues and actions associated to controls / risks are remediated in a timely manner 
Maintain appropriate records on iCARE or Archer 
Ensure that controls are sufficiently well designed and operating effectively to keep the risks that they mitigate within client's tolerance level
Report and escalate the status of the relevant risks, controls, and standards as appropriate
Resource Complexity:
Accountable for working within policy and guidelines, applying technical knowledge and expertise, and prioritizing own use of time
Problem Solving:
The essence of the role is project- based problem solving. 
Accountable for solving problems and dealing with difficulties in line with policy, process and other guidelines applying technical knowledge and expertise 
Depending on the nature of the specific role, problems can range from repetitive daily issues to complex technical problems requiring significant expertise 
Escalate problems according to guidelines
Change:
Recommend design solutions or ideas in response to challenges faced by your project or feature team in line with policy, process, and other guidelines. 
Accountable for recommending change based on expert know how and analysis 
Take personal initiative in adapting to change
Internal Collaboration: Build effective working relationships with
Project managers and others on projects
Platform Managers and Specialist
Platform Architects and Designers
External Interaction:
Maintain liaison with suppliers and the relevant sources of technology to ensure the specialist knowledge is kept up to date.

Capabilities:
Proven experience in solution architecture, roadmaps and architecture design and implementation. 
Knowledge of wider application technologies stack, and Infrastructure knowledge 
Knowledge of cloud native and modern application architecture 
Experience in building roadmap with interim state. 
Strong knowledge of end-to-end project lifecycle methodologies (agile). 
Financial acumen to support development of business cases and to inform key design decisions. 
Ability to balance long-term direction and necessary short-term goals. 
Effective communication skills with the ability to provide technical guidance to peers and project colleagues. 
Strong personal impact and influence 
Good stakeholder and relationship management 
Leadership skills

Benefits/perks listed below may vary depending on the nature of your employment with LTIMindtree (“LTIM”):
Benefits and Perks:
Comprehensive Medical Plan Covering Medical, Dental, Vision
Short Term and Long-Term Disability Coverage
401(k) Plan with Company match
Life Insurance
Vacation Time, Sick Leave, Paid Holidays
Paid Paternity and Maternity Leave

The range displayed on each job posting reflects the minimum and maximum salary target for the position across all US locations. Within the range, individual pay is determined by work location and job level and additional factors including job-related skills, experience, and relevant education or training. Depending on the position offered, other forms of compensation may be provided as part of overall compensation like an annual performance-based bonus, sales incentive pay and other forms of bonus or variable compensation.

Disclaimer: The compensation and benefits information provided herein is accurate as of the date of this posting.

LTIMindtree is an equal opportunity employer that is committed to diversity in the workplace. Our employment decisions are made without regard to race, color, creed, religion, sex (including pregnancy, childbirth or related medical conditions), gender identity or expression, national origin, ancestry, age, family-care status, veteran status, marital status, civil union status, domestic partnership status, military service, handicap or disability or history of handicap or disability, genetic information, atypical hereditary cellular or blood trait, union affiliation, affectional or sexual orientation or preference, or any other characteristic protected by applicable federal, state, or local law, except where such considerations are bona fide occupational qualifications permitted by law.

Safe return to office:
In order to comply with LTIMindtree’ s company COVID-19 vaccine mandate, candidates must be able to provide proof of full vaccination against COVID-19 before or by the date of hire. Alternatively, one may submit a request for reasonable accommodation from LTIMindtree’s COVID-19 vaccination mandate for approval, in accordance with applicable state and federal law, by the date of hire. Any request is subject to review through LTIMindtree’s applicable processes.",0,In Development,"Dhiraj Kumar 
Dhiraj Kumar is verified",https://www.linkedin.com/in/dhiraj-kumar-977b0420b,Previous resume,False,2024-08-29 10:45:17.773871,2024-09-05 10:45:21.839096,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('how many years of work experience do you have with application architecture?', '7', 'text', ''), ('Do you have a valid driver\'s license? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with technology roadmapping?', '7', 'text', ''), ('Are you willing to take a drug test, in accordance with local law/regulations? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('Are you open to work from Markham, ON office for 03 days a week? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('how many years of work experience do you have with solution architecture?', '7', 'text', ''), ('Are you willing to undergo a background check, in accordance with local law/regulations? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
**********,"Architecte / Modélisation de données AWS Redshift / S3
Architecte / Modélisation de données AWS Redshift / S3 with verification",Systematix,"Montreal, QC",Hybrid,"About the job
Mode hybride: 1 journée aux 2 semaines en présentiel au centre-ville de Montréal, ou selon les besoins de la compagnie. 

Date de début: le 14-oct-2024 
Date de fin: le 31-dec-2025 

Mise en contexte
Le conseiller travaillera sur le calculateur de prévision de la demande, à court, moyen et long terme, pour optimiser le service aux consommateurs, et optimiser le système. Le projet aussi aidera à exploiter des nouveaux modèles, perfectionner les modèles actuels, et autres. 

Portée technologique des services
Les applications utilisées et outils sont :Systèmes d’exploitation seront Windows et Linux.
Les outils de programmation pour un ou plusieurs langages (Java, Angular), patron de développement, Api Rest (authentification, autorisation, traitement de données).
GIT et Bitbucket, outils Jira, Confluence
Technologies infonuagique AWS.

Profils recherchés
Avoir une très bonne connaissance: 
en architecture dans des projets de plateformes infonuagiques de grande envergure, de plateformes infonuagique publique, privée et hybride, incluant l'automatisation; en architecture de conteneurisation, en architecture de stockage.
des différents services de gestion de base de données tant structurée que non structurée.
 des meilleures pratiques de sécurisation des plateformes technologiques.
des techniques/outils permettant l’intégration et le déploiement en continu (CI/CD).
Avoir une bonne connaissance des référentiels d'architecture d'entreprise tel que TOGAF ou Zachman.
Expérience avec applications OLAP, avec applications prévisionnelles et avec applications temps réel.
Expérience avec les SDK de AWS, particulièrement pour les technologies S3 et Redshift.",0,In Development,"Elizabeth Slanke 
Elizabeth Slanke is verified",https://www.linkedin.com/in/elizabeth-slanke-b45005201,Previous resume,False,2024-08-22 10:45:26.396048,2024-09-05 10:45:31.085587,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('numéro de téléphone portable', '**********', 'text', '**********'), ('Adresse e-mail [  ""Sélectionnez une option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('depuis combien d’années utilisez-vous amazon s3 ?', '7', 'text', ''), ('Code pays [  ""Sélectionnez une option"", ""Canada (+1)"", ""Afghanistan (+93)"", ""Afrique du Sud (+27)"", ""Albanie (+355)"", ""Algérie (+213)"", ""Allemagne (+49)"", ""Andorra (+376)"", ""Angola (+244)"", ""Anguilla (+1)"", ""Antarctique (+0)"", ""Antigua-et-Barbuda (+1)"", ""Antilles néerlandaises (+0)"", ""Arabie Saoudite (+966)"", ""Argentine (+54)"", ""Arménie (+374)"", ""Aruba (+297)"", ""Australie (+61)"", ""Autriche (+43)"", ""Azerbaïdjan (+994)"", ""Bahamas (+1)"", ""Bahreïn (+973)"", ""Bangladesh (+880)"", ""Barbade (+1)"", ""Belgique (+32)"", ""Belize (+501)"", ""Bermudes (+1)"", ""Bhoutan (+975)"", ""Biélorussie (+375)"", ""Bolivie (+591)"", ""Bosnie-Herzégovine (+387)"", ""Botswana (+267)"", ""Brunei (+673)"", ""Brésil (+55)"", ""Bulgarie (+359)"", ""Burkina Faso (+226)"", ""Burundi (+257)"", ""Bénin (+229)"", ""Cambodge (+855)"", ""Cameroun (+237)"", ""Cap Vert (+238)"", ""Chili (+56)"", ""Chine (+86)"", ""Chypre (+357)"", ""Cité du Vatican (Saint-Siège) (+39)"", ""Colombie (+57)"", ""Comores (+269)"", ""Congo (+242)"", ""Corée du Nord (+850)"", ""Corée du Sud (+82)"", ""Costa Rica (+506)"", ""Croatie (+385)"", ""Cuba (+53)"", ""Côte d\'Ivoire (+225)"", ""Danemark (+45)"", ""Djibouti (+253)"", ""Dominique (+1)"", ""Espagne (+34)"", ""Estonie (+372)"", ""Fidji (+679)"", ""Finlande (+358)"", ""France (+33)"", ""Fédération de Russie (+7)"", ""Gabon (+241)"", ""Gambie (+220)"", ""Ghana (+233)"", ""Gibraltar (+350)"", ""Grenade (+1)"", ""Groenland (+299)"", ""Grèce (+30)"", ""Guadeloupe (+590)"", ""Guam (+1)"", ""Guatemala (+502)"", ""Guernesey (+44)"", ""Guinée (+224)"", ""Guinée équatoriale (+240)"", ""Guinée-Bissau (+245)"", ""Guyana (+592)"", ""Guyane française (+594)"", ""Géorgie (+995)"", ""Haïti (+509)"", ""Honduras (+504)"", ""Hong Kong (+852)"", ""Hongrie (+36)"", ""Inde (+91)"", ""Indonésie (+62)"", ""Irak (+964)"", ""Iran (+98)"", ""Irlande (+353)"", ""Islande (+354)"", ""Israël (+972)"", ""Italie (+39)"", ""Jamaïque (+1)"", ""Japon (+81)"", ""Jersey (+44)"", ""Jordanie (+962)"", ""Kazakhstan (+7)"", ""Kenya (+254)"", ""Kirghizistan (+996)"", ""Kiribati (+686)"", ""Kosovo (+383)"", ""Koweït (+965)"", ""Laos (+856)"", ""Lesotho (+266)"", ""Lettonie (+371)"", ""Liban (+961)"", ""Liberia (+231)"", ""Libye (+218)"", ""Liechtenstein (+423)"", ""Lituanie (+370)"", ""Luxembourg (+352)"", ""Macao (+853)"", ""Macédoine (+389)"", ""Madagascar (+261)"", ""Malaisie (+60)"", ""Malawi (+265)"", ""Maldives (+960)"", ""Mali (+223)"", ""Malte (+356)"", ""Maroc (+212)"", ""Martinique (+596)"", ""Maurice (+230)"", ""Mauritanie (+222)"", ""Mayotte (+262)"", ""Mexique (+52)"", ""Moldavie (+373)"", ""Monaco (+377)"", ""Mongolie (+976)"", ""Montserrat (+1)"", ""Monténégro (+382)"", ""Mozambique (+258)"", ""Myanmar (Birmanie) (+95)"", ""Namibie (+264)"", ""Nations caribéennes (+0)"", ""Nauru (+674)"", ""Nicaragua (+505)"", ""Niger (+227)"", ""Nigeria (+234)"", ""Niue (+683)"", ""Norfolk (+672)"", ""Norvège (+47)"", ""Nouvelle-Calédonie (+687)"", ""Nouvelle-Zélande (+64)"", ""Népal (+977)"", ""Oman (+968)"", ""Ouganda (+256)"", ""Ouzbékistan (+998)"", ""Pakistan (+92)"", ""Palau (+680)"", ""Panama (+507)"", ""Papouasie-Nouvelle-Guinée (+675)"", ""Paraguay (+595)"", ""Pays-Bas (+31)"", ""Philippines (+63)"", ""Pitcairn (+0)"", ""Pologne (+48)"", ""Polynésie française (+689)"", ""Porto Rico (+1)"", ""Portugal (+351)"", ""Pérou (+51)"", ""Qatar (+974)"", ""Roumanie (+40)"", ""Royaume-Uni (+44)"", ""Rwanda (+250)"", ""République Centrafricaine (+236)"", ""République Démocratique du Congo (+243)"", ""République dominicaine (+1)"", ""République tchèque (+420)"", ""Réunion (+262)"", ""Sahara occidental (+212)"", ""Saint Pierre et Miquelon (+508)"", ""Saint-Christophe-et-Niévès (+1)"", ""Saint-Marin (+378)"", ""Saint-Vincent-et-les-Grenadines (+1)"", ""Sainte-Hélène (+290)"", ""Sainte-Lucie (+1)"", ""Salvador (+503)"", ""Samoa (+685)"", ""Samoa (U.S.) (+1)"", ""Sao Tomé-et-Principe (+239)"", ""Serbie (+381)"", ""Serbie et Monténégro (+0)"", ""Seychelles (+248)"", ""Sierra Leone (+232)"", ""Singapour (+65)"", ""Slovaquie (+421)"", ""Slovénie (+386)"", ""Somalie (+252)"", ""Soudan (+249)"", ""Soudan du Sud (+211)"", ""Sri Lanka (+94)"", ""Suisse (+41)"", ""Suriname (+597)"", ""Suède (+46)"", ""Svalbard et Jan Mayen (+47)"", ""Swaziland (+268)"", ""Syrie (+963)"", ""Sénégal (+221)"", ""Tadjikistan (+992)"", ""Tanzanie (+255)"", ""Taïwan (+886)"", ""Tchad (+235)"", ""Territoire Britannique de l\'Océan Indien (+246)"", ""Territoires Français du Sud (+0)"", ""Territoires palestiniens (+970)"", ""Thaïlande (+66)"", ""Timor oriental (+670)"", ""Togo (+228)"", ""Tokelau (+690)"", ""Tonga (+676)"", ""Trinité-et-Tobago (+1)"", ""Tunisie (+216)"", ""Turkménistan (+993)"", ""Turquie (+90)"", ""Tuvalu (+688)"", ""Ukraine (+380)"", ""Uruguay (+598)"", ""Vanuatu (+678)"", ""Venezuela (+58)"", ""Vietnam (+84)"", ""Wallis et Futuna (+681)"", ""Yémen (+967)"", ""Zambie (+260)"", ""Zimbabwe (+263)"", ""Égypte (+20)"", ""Émirats arabes unis (+971)"", ""Équateur (+593)"", ""Érythrée (+291)"", ""États Fédérés de Micronésie (+691)"", ""États-Unis (+1)"", ""Éthiopie (+251)"", ""Île Bouvet (+0)"", ""Île Christmas (+61)"", ""Île Heard et îles McDonald (+0)"", ""Île de Man (+44)"", ""Îles Aland (+358)"", ""Îles Caïman (+1)"", ""Îles Cocos (Keeling) (+61)"", ""Îles Cook (+682)"", ""Îles Féroé (+298)"", ""Îles Malouines (Falkland) (+500)"", ""Îles Mariannes du nord (+1)"", ""Îles Marshall (+692)"", ""Îles Salomon (+677)"", ""Îles Turques et Caïques (+1)"", ""Îles Vierges (U.S.A.) (+1)"", ""Îles Vierges (britanniques) (+1)"", ""Îles de Géorgie du Sud et îles Sandwich du Sud (+0)"", ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4015541529,"Solutions Architect
Solutions Architect with verification",Bevertec,"Toronto, ON",On-site,"About the job
Solution Architect - CRM 
Must-Have:
Experience in Converting Monolithic to Microservices Architecture: Demonstrated experience in converting monolithic applications into microservices architecture or a strong understanding of the same.
Java Backend Experience: Hands-on experience with Java backend development, including working with frameworks, APIs, and integration with various services.
Hands-on Experience: Proven hands-on experience in solutions design and development, with the ability to implement and manage CRM solutions in large, distributed environments.
Understanding of System Interfaces: Ability to monitor entire systems, understand all interactions between components at various levels of abstraction, and define limitations and requirements for system interfaces and source code.
CRM Solution Architecture: Demonstrated experience in leading the development and implementation of CRM solution architectures, with extensive knowledge of CRM applications, operating systems, database technologies, and communication protocols.

General Skills:
Define the structure and relationships within CRM solutions, focusing on re-usable components, quality metrics, software organization, and interface determination.
Develop, implement, and manage CRM solutions in large, distributed environments, ensuring comprehensive monitoring and understanding of system interactions and component integration.
Identify limitations and requirements for source code and define interactions between application packages, databases, and middleware systems.
Understanding of performance considerations across various environments.
Experience in structured methodologies for application design, development, and implementation.
Proficient in working with middleware and gateways.
Familiarity with development tools, CASE tools, information retrieval packages, and other relevant software tools, including project management and business re-engineering software.
Skilled in data and process modeling methodologies, with knowledge of metadata structures, repository functions, and data dictionaries.
Experience in developing enterprise architecture deliverables (e.g., models).
Awareness of emerging IT trends and directions.
Excellent analytical, problem-solving, decision-making, verbal and written communication, interpersonal, and negotiation skills.
A proven team player with a track record of meeting deadlines.

 Skills
 Experience and Skill Set Requirements
 Technical Experience – 75% Java and API and microservices and enterprise and agile and CRM and architecture
Strong understanding of designing and implementing microservices architectures, including the decomposition of monolithic applications, service orchestration, and the integration of microservices with legacy on-premise systems.
Strong understanding of modern cloud-based integrations involving private, sensitive data with on-premise legacy solutions.
Proven ability to design architecture patterns across large systems, including defining end-to-end governance models for operations and integration into hybrid solutions.
Extensive experience in planning, creating, and implementing enterprise-level architectures, utilizing architectural patterns, frameworks, and best practices to build scalable, maintainable, and robust solutions.
Proficient in integrating and utilizing Software-as-a-Service (SaaS) and Platform-as-a-Service (PaaS) solutions within existing architectures, with strong knowledge of multi-tenancy, cloud service models, and API integrations.
Experience in developing, implementing, and managing CRM solutions in large, distributed environments. Ability to monitor entire systems, understand all interactions between components, and define limitations and requirements for system interfaces and source code.
Proven experience with Microsoft Azure services, including designing, deploying, and managing Azure cloud infrastructure and identity models like Azure Active Directory (B2C, B2B).
Strong understanding of cybersecurity principles to ensure solutions comply with OPS regulations, including identity management, data encryption, and threat mitigation strategies.
Experience with structured methodologies for the design, development, and implementation of applications, as well as proficiency with middleware and gateways.
Knowledge and understanding of Information Management principles, concepts, policies, and practices.
Experience working in Agile teams, leveraging tools such as Azure DevOps and Jira.
Awareness of emerging IT trends and directions, ensuring solutions remain forward-thinking and relevant.

 Communication and Leadership Skills – 25%
Familiarity with government procedures for the procurement and deployment of technology solutions.
Ability to establish standard practices across different teams for effective transformation to modern technology architecture.
Capable of developing both short-term and long-term plans for the transformation of architecture.
Effective communication skills to liaise between technical and non-technical stakeholders, ensuring clear and concise information exchange.
Demonstrated ability to lead multidisciplinary teams towards the successful completion of projects.
Ability to develop comprehensive enterprise architectural deliverables (e.g., models, documentation) and understand the integration requirements from both high-level and low-level perspectives.
Experience working with project managers and lead architects to scope work, identify technical risks, and manage priorities effectively.
Knowledge of Ontario government IT protocols, security standards, and regulations is preferred.",0,In Development,Unknown,Unknown,Previous resume,False,2024-09-04 10:45:40.079490,2024-09-05 10:45:43.501078,https://www.linkedin.com/jobs/view/4015541529,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Hands-on Experience: Proven hands-on experience in solutions design and development, with the ability to implement and manage CRM solutions in large, distributed environments. [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with java?', '1', 'text', '1'), ('how many years of work experience do you have with platform as a service (paas)?', '7', 'text', ''), ('how many years of work experience do you have with software as a service (saas)?', '7', 'text', ''), ('• Experience in Converting Monolithic to Microservices Architecture:  [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('crm solution architecture: demonstrated experience in leading the development and implementation of crm solution architectures?', '7', 'text', '')}",In Development
**********,"solution designer
solution designer with verification",Insight Global,"Toronto, ON",Hybrid,"About the job
JOB DESCRIPTION
Insight Global is looking for an Engineer/Consultant to join one of Canada's top 5 banks to support a data and cloud team for a multitude of projects. The Senior Consultant, Data Engineering Analysis & Design is responsible for leading the functional design and development of application systems across all technologies and platforms. The role provides consultation for senior management on a wide spectrum of existing and emerging infrastructure technologies, translating user requirements to process and data requirements and developing solutions for the business with far-reaching impact. The Senior Consultant, Data Engineering Analysis & Design provides technical leadership to develop precise steps and processing logic across all platforms (Azure Databricks, Data Factory etc), ensuring the designed specifications achieve business needs. In addition, the role provides comprehensive senior level technical consulting to other IT senior management and senior technical teams. The role makes technology selection decisions that are often made in situations where business requirements are not completely defined. At times, high level solution needs to be determined based on business requirement which is defined only at high level, and/or would be evolving in phases. **Lead experience is not required, a strong Engineer who wants to pursue Design, Architecture or Lead experience would be well suited for this role as you will train with a Lead and ramp you up over 6-12months. The above responsibilities are where we intend this role will be after ramp up, but strong development skills coupled with a strong understanding of data and Azure will make you a good fit to start this position. They intend for it to go full time after so preference is for candidates who are open to that after the contract. Core office days are Mondays and the first Friday of each month.

REQUIRED SKILLS AND EXPERIENCE
• Strong understanding of Azure Cloud platform and how it works/add-ons/intricacies
• Azure data bricks • Data factory
• Purview
• Development and Engineering background - don’t need to be a designer, or a lead or have that experience, just an interest in pursuing it
•Strong communication skills and ability to work with various teams, executives, vendors, etc.

NICE TO HAVE SKILLS AND EXPERIENCE
•Solution Design experience
•Lead experience
•Banking experie",0,In Development,Unknown,Unknown,Previous resume,False,2024-09-04 15:45:49.602229,2024-09-05 10:45:52.459392,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('mobile phone number', '**********', 'text', '**********'), (' Strong understanding of Azure Cloud platform and how it works/add-ons/intricacies [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Azure data bricks and Data Factory Expertise  [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('Development and Engineering background  [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option')}",In Development
**********,"Senior Solutions Architect (Broadridge & Wealth Management)
Senior Solutions Architect (Broadridge & Wealth Management) with verification",Collabera,"Greater Toronto Area, Canada",Hybrid,"About the job
Daily Responsibilities:
As a program architect, works with individual solution architects and resolves complex cross-cutting issues through the analysis of requirements, technical expertise, and architecture oversight. Applies extensive, in-depth knowledge of Wealth Management processes and best technology practices to deliver a unified solution for a large, complex program.

What program/technology/software knowledge is essential for this role?
Strong technical background – Microservices, GraphQL, Kafka, Kubernetes, Cloud,
Must have experience building high-performing critical systems
Solid knowledge of applied architecture patterns
Solid knowledge of architecture governance processes

Must Have Skills:
Must understand Wealth Management business; have solid experience with Broadridge WM custodial systems, understand Portfolio Management lifecycle and Order management and execution

About Us: 
Collabera is a leading Digital Solutions company providing software engineering solutions to the world’s most tech-forward organizations. With more than 25 years of experience, we have hired over 17000 employees across 60+ offices globally and currently place 10000+ professionals annually to support critical IT engagements at more than 500 client sites, 80% being the Fortune 500. {and 59% of the Fortune 50 (could use either stat)}

With Collabera, you 
• Will get to work on numerous challenging and exciting projects, including UI/UX transformation, Blockchain, AI/Data Science, Cloud migrations, Cyber-Security and Engineering.
• At Collabera you have 80% chances of project extension or redeployment to other clients
• Will have endless opportunities to learn new technologies through our inhouse Training arm – Cognixia.",Error in extraction,In Development,"Rahul Chhatrapati 
Rahul Chhatrapati is verified",https://www.linkedin.com/in/rahulchhatrapati,Previous resume,False,2024-09-05 10:11:04.822136,2024-09-05 10:46:07.503417,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of experience do you have in solutions architecture?', '7', 'text', ''), ('how many years of work experience do you have with graphql?', '7', 'text', ''), ('how many years of experience do you have supporting wealth management or broadridge projects ?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4015834369,"Solutions Architect
Solutions Architect with verification",Raise,"Toronto, ON",Hybrid,"About the job
Job Description:
The Solution Architect is responsible for designing, developing, and implementing technology solutions that meet business requirements and align with the organization’s strategic goals. This role involves working closely with stakeholders, including business leaders, developers, and project managers, to ensure that solutions are technically sound, scalable, and cost-effective. The Solution Architect provides technical leadership throughout the project lifecycle, from initial concept through to implementation and support.

DUTIES
Develop a technology roadmap and provide guidance for bringing business ideas to the public through mobile applications for Canada post focusing on iOS, Android, and Firebase.
Because of the unique challenges and constraints in mobile app development, such as limited resources (memory, processing power), diverse device ecosystems, and the need for responsive and user-friendly interfaces we require a specific SA with specific knowledge
Will need to work with business to do discovery and IT reviews.
There are a significant number of business initiatives in Returns, My Mail and architectural obsolescence that needs to be resolved.
SKILLS AND EXPERIENCES:
Technical Skills
Mobile Development: Expertise in iOS (Swift, Objective-C) and Android (Kotlin, Java) development.
Cross-Platform Development: Knowledge of tools like Flutter and even React Native for building cross-platform applications to help define the technology capabilities needed by the business.
Backend Integration: Proficiency in integrating mobile apps with backend services, especially using Firebase for real-time databases, authentication, and cloud functions.
API Design and Management: Skills in designing, developing, and managing APIs, ensuring they are secure and scalable.
UI/UX Design: Understanding of user interface and user experience design principles to create intuitive and engaging mobile applications.
Security: Knowledge of mobile security best practices to protect user data and ensure secure transactions.
Architectural Patterns:
A mobile architect needs to be familiar with various design patterns to ensure that mobile applications are scalable, maintainable, and efficient 
Model View View Model ( MVVM), Model view presenter ( MVP), dependency injection, Composite
Data source (local db, remote ap) 
Ensuring a clear separation of concerns, enhancing testability, and promoting code reuse.
Analytical and Problem-Solving Skills
Systems Thinking: Ability to model complex systems and design coherent architectures that integrate mobile applications with other enterprise systems.
Analytical Mindset: Strong problem-solving skills to address technical challenges and optimize mobile solutions.",0,In Development,Unknown,Unknown,Previous resume,False,2024-09-05 09:46:14.653979,2024-09-05 10:46:16.707294,https://www.linkedin.com/jobs/view/4015834369,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('mobile phone number', '**********', 'text', '**********')}",In Development
**********,"Solutions Architect
Solutions Architect with verification",LTIMindtree,"Markham, ON",On-site,"About the job
About Us:
LTIMindtree is a global technology consulting and digital solutions company that enables enterprises across industries to reimagine business models, accelerate innovation, and maximize growth by harnessing digital technologies. As a digital transformation partner to more than 700+ clients, LTIMindtree brings extensive domain and technology expertise to help drive superior competitive differentiation, customer experiences, and business outcomes in a converging world. Powered by nearly 90,000 talented and entrepreneurial professionals across more than 30 countries, LTIMindtree — a Larsen & Toubro Group company — combines the industry-acclaimed strengths of erstwhile Larsen and Toubro Infotech and Mindtree in solving the most complex business challenges and delivering transformation at scale. For more information, please visit www.ltimindtree.com.
 Job Title: 
Azure ADF Data Engineer 

Work Location
Portland, Oregon

Job Description:
Role Purpose:
The purpose of the Solution Architect role is to:
Define end-to-end solutions for IT change primarily focused on the data domain
Create designs that are fit for purpose from a functional, non-functional, service, and security point of view
Deliver solutions that progress the overall architecture journey and are aligned to Client’s IT Strategy
Comply with standards, design approaches and governance
Accountabilities
Nature of Work
Solutons and Assurance
Create data solutions and design for projects to meet Client’s standards for technology and ensure
they meet architecture principle of availability, security, resilience, and performance
Produce design options with associated costs, benefits, and risks to enable investment decisions
Work with product owners, architects, platform teams and other project colleagues to agree tradeoffs between, long-term and short-term goals
Responsible for the integrity of the end-to-end solution across Platforms, including transition states
used to achieve the target state
Shape and estimate IT initiatives and feature team sprints, ensuring that component parts deliver against business outcomes
Ability to communicate complex technical concepts to both technical and non-technical audiences.
Record any solution debt incurred during the transition phase of the solution and address the same in future phases
Identify potential risks and challenges associated with proposed solutions.
Develop mitigation strategies to address identified risks.
Participate in post-implementation reviews and provide feedback to improve future solution designs

Technical Leadership 
Keep up with technological developments in the data and analy􀆟cs area and evaluate how well they fit
into the company's architecture.
Evaluate and recommend tools, frameworks, and platforms to support the development and deployment of data and analytical solutions
Support defining the architecture best practices and standards and enforcing them in the project solutions.
Conduct architecture reviews to ensure compliance with established guidelines
Ensure design artefacts are produced to support stakeholder group for service, security, architecture, and engineering
Peer reviews the design artefacts of others
Provide coaching, mentoring, and training to less experienced team members as required
Design Community membership
Contribute to the Solution Architecture community across Client’s Group leading on or par􀆟cipa􀆟ng in
initiatives to improve Client’s approach to design
Develop your technical skills to support Client’s need to understand and exploit emerging technology
trends

Risk & Controls
Identify, own, and manage the specific key risks and/or IT controls and BP standards that you are
identified as the owner and/or nominee for on iCARE or Archer
Ensure that issues and actions associated to controls / risks are remediated in a timely manner
Maintain appropriate records on iCARE or Archer
Ensure that controls are sufficiently well designed and opera􀆟ng effectively to keep the risks that they mitigate within Client's tolerance level
Report and escalate the status of the relevant risks, controls, and standards as appropriate Resource Complexity
Accountable for working within policy and guidelines, applying technical knowledge and expertise, and prioritizing own use of 􀆟me
Problem Solving
The essence of the role is project- based problem solving.
Accountable for solving problems and dealing with difficulties in line with policy, process and otherguidelines applying technical knowledge and expertise 
Depending on the nature of the specific role, problems can range from repetitive daily issues to complex
technical problems requiring significant expertise
Escalate problems according to guidelines Change
Recommend design solutions or ideas in response to challenges faced by your project or feature team in line with policy, process, and other guidelines.
Accountable for recommending change based on expert know how and analysis
Take personal initiative in adapting to change Internal collaboration
Build effective working relationships with:
Project managers and others on projects
Platform Managers and Specialist
Platform Architects and Designers
External interaction
Maintain liaison with suppliers and the relevant sources of technology to ensure the specialist knowledge is kept up to date.


Benefits/perks listed below may vary depending on the nature of your employment with LTIMindtree (“LTIM”):

Benefits and Perks:
Comprehensive Medical Plan Covering Medical, Dental, Vision
Short Term and Long-Term Disability Coverage
401(k) Plan with Company match
Life Insurance
Vacation Time, Sick Leave, Paid Holidays
Paid Paternity and Maternity Leave

The range displayed on each job posting reflects the minimum and maximum salary target for the position across all US locations. Within the range, individual pay is determined by work location and job level and additional factors including job-related skills, experience, and relevant education or training. Depending on the position offered, other forms of compensation may be provided as part of overall compensation like an annual performance-based bonus, sales incentive pay and other forms of bonus or variable compensation.

Disclaimer: The compensation and benefits information provided herein is accurate as of the date of this posting.

LTIMindtree is an equal opportunity employer that is committed to diversity in the workplace. Our employment decisions are made without regard to race, color, creed, religion, sex (including pregnancy, childbirth or related medical conditions), gender identity or expression, national origin, ancestry, age, family-care status, veteran status, marital status, civil union status, domestic partnership status, military service, handicap or disability or history of handicap or disability, genetic information, atypical hereditary cellular or blood trait, union affiliation, affectional or sexual orientation or preference, or any other characteristic protected by applicable federal, state, or local law, except where such considerations are bona fide occupational qualifications permitted by law.
 Safe return to office:
In order to comply with LTIMindtree’ s company COVID-19 vaccine mandate, candidates must be able to provide proof of full vaccination against COVID-19 before or by the date of hire. Alternatively, one may submit a request for reasonable accommodation from LTIMindtree’s COVID-19 vaccination mandate for approval, in accordance with applicable state and federal law, by the date of hire. Any request is subject to review through LTIMindtree’s applicable processes.",0,In Development,"Disha K. 
Disha K. is verified",https://www.linkedin.com/in/disha-k-*********,Previous resume,False,2024-08-22 10:46:24.427416,2024-09-05 10:46:27.855387,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Do you have a valid driver\'s license? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('Will you now or in the future require sponsorship for employment visa status? [  ""Yes""<Yes>, ""No""<No>, ]', 'No', 'radio', None), ('how many years of work experience do you have with solution architecture?', '7', 'text', '7'), ('how many years of work experience do you have with amazon web services (aws)?', '1', 'text', '1'), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('how many years of work experience do you have with snowflake?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
**********,"Business Objects Developer
Business Objects Developer with verification",VLink Inc,"Montreal, QC",On-site,"About the job
We are seeking a skilled and motivated Business Objects Developer/Data Support to join our team. Candidate will collaborate, analyze, design, develop, test, maintain and implement premier software while working with cross-functional teams such as product and architecture.

Responsibilities:
  Deliver accurate data in a timely manner to key business users (BAU requests, ServiceNow Tickets requests).
Implement and manage company data management practices and policies.
Undertake performance turning and troubleshooting.
Perform remediation of reporting hygiene issues.
Implement continuous improvement.
Mentor skills within the reporting team.

Requirements: 
  Strong knowledge in writing SQLs using RDBMS like Sybase, DB2.
Deep understanding of defect tracking tool such as Jira.
Experience/exposure to version control tools such as GIT.
Experience of working in Agile teams.
Experience in the PowerBI reporting tool (optional).
Good analytical and problem-solving skills.
Good interpersonal and communication skills.
Ability to work under time and resource constraints and find simple and effective solutions.
Motivated to expand technical and business knowledge.

Good to have:
  Exposure to other reporting tools like Tableau, BusinessObjects.
Exposure to reporting using cloud technologies.
Experience/exposure to scripting using Python, Unix/Linux shell.
Experience in BOXI report development and universe building.
Good understanding of user security in BO and additional knowledge on CMC is a plus.",0,In Development,"Amit Kumar 
Amit Kumar is verified",https://www.linkedin.com/in/amit-kumar-598b547,Previous resume,False,2024-08-15 10:47:00.682700,2024-09-05 10:47:03.648046,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of work experience do you have with linux?', '7', 'text', ''), ('how many years of work experience do you have with python (programming language)?', '3', 'text', '3'), ('how many years of work experience do you have with unix?', '7', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4013665479,"Platform Engineer / DevOps Engineer - Elite FinTech - $100,000-$150,000 CAD + Bonus
Platform Engineer / DevOps Engineer - Elite FinTech - $100,000-$150,000 CAD + Bonus with verification",Hunter Bond,"Montreal, QC",Hybrid,"About the job
Job title: Platform Engineer / DevOps Engineer
Client: Elite FinTech
Salary: $100,000-$200,000 CAD
Location: Montreal / Hybrid
Skills: Linux, Chef, Puppet, Ansible, Kubernetes, Docker, CI/CD, Python

The role:

My client are seeking a knowledgeable Platform Engineer to work on their low latency Linux estate.

The role sits between Platform Engineering, DevOps, Linux Systems Administration and SRE, and incorporates elements of all these positions.

You will build, design and architect automated solutions for scalable deployment, in private and public cloud infrastructure.

You will work in both a project and BAU capacity, automating wherever possible.

Core skills required:

Linux
Chef, Puppet or Ansible
Kubernetes, Docker, or Podman
CI/CD
Python, Ruby or Go

Desirable skills:

IaC – Terraform, Helm, Ansible, Pulumi, Bicep
AWS or GCP
DR
Block / Blob Storage
HPC

Please apply ASAP for more information.",0,In Development,Unknown,Unknown,Previous resume,False,2024-09-02 10:47:11.501718,2024-09-05 10:47:13.339861,https://www.linkedin.com/jobs/view/4013665479,Easy Applied,"{('phone', '**********', 'text', '**********'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>')}",In Development
4000704712,"Java Scala Developer
Java Scala Developer with verification",Genpact,"Montreal, QC",Hybrid,"About the job
Greetings
With a startup spirit and 125,000+ curious and courageous minds, we have the expertise to go deep with the world’s biggest brands—and we have fun doing it. Now, we’re calling all you rule-breakers and risk-takers who see the world differently and are bold enough to reinvent it. Come, transform with us.

Are you the One we are looking for?

Inviting applications for the role of Java Scala Developer at Montreal, QC, Fulltime

Skills Required:
Experience in Java/Scala development.
Sound knowledge of Spring and Springboot.
Exposure to Restful APIs.
Understanding of Database concepts and SQL / Stored Procedures.
Exposure to Git and Jenkins CI/CD pipeline.

Good to have: 
Spark SQL
Python
Exposure to Agile methodology
Finance domain knowledge.
Self-starter with ability to work in a fast-paced environment and be able to work on multiple projects.


""Genpact is an Equal Opportunity Employer and considers applicants for all positions without regard to race, color, religion or belief, sex, age, national origin, citizenship status, marital status, military/veteran status, genetic information, sexual orientation, gender identity, physical or mental disability or any other characteristic protected by applicable laws. Genpact is committed to creating a dynamic work environment that values diversity and inclusion, respect and integrity, customer focus, and innovation. For more information, visit www.genpact.com . Follow us on Twitter, Facebook, LinkedIn, and YouTube.

Furthermore, please do note that Genpact does not charge fees to process job applications and applicants are not required to pay to participate in our hiring process in any other way. Examples of such scams include purchasing a 'starter kit,' paying to apply, or purchasing equipment or training.",0,In Development,Unknown,Unknown,Previous resume,False,2024-08-22 10:47:24.642243,2024-09-05 10:47:26.799658,https://www.linkedin.com/jobs/view/4000704712,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('mobile phone number', '**********', 'text', '**********')}",In Development
3996451517,"Développeur BI (Google)
Développeur BI (Google) with verification",Astek,"Montreal, QC",Remote,"About the job
Le Groupe Astek

Créé en France en 1988, Astek est un acteur mondial de l’ingénierie et du conseil en technologies. Fort de son expertise dans de nombreux secteurs industriels et tertiaires, Astek accompagne ses clients internationaux dans le déploiement intelligent de leurs produits et de leurs services, et dans la mise en œuvre de leur transformation digitale.

Depuis sa création, le Groupe a fondé son développement sur une forte culture d’entrepreneuriat et d’innovation, et sur l’accompagnement et la montée en compétence de ses 7800 collaborateurs qui s’engagent chaque jour à promouvoir la complémentarité entre les technologies numériques et l’ingénierie des systèmes complexes.

Au Canada, la firme se spécialise en service conseil TI et en ingénierie. Nos équipes pilotent avec succès les stratégies de développement de nos clients. Autour de valeurs communes, nous avons gagné leur confiance et répondons à leurs enjeux d’entreprise en mettant leurs besoins au centre de nos actions et priorités. 

Cet écosystème d'expertises permet à Astek Canada de s’adapter rapidement à l’évolution des technologies, et tous les collaborateurs participent de manière collaborative à leur développement. 

Tu es prêt à relever un nouveau défi, donner un boost à ta carrière et participer à des projets innovants et stimulants ? 

Rejoins un Groupe en fort développement au Canada et à travers le monde ayant réalisé un chiffre d’affaires de 600 M€ en 2023.



Ta mission serait : 

• Développer des automatismes d’extraction, transformation et chargement de données à partir de systèmes opérationnels (ETL /Pipelines et cheminement des données au sein de l’infrastructure BI);
• Contribuer à la mise en place des modèles conceptuel, logique et physique de données;
• Développer, organiser et mettre en place des comptoirs de données transformés (cubes);
• Assurer le support et/ou le suivi concernant l’extraction des données des systèmes d’information;
• Participer à l’opérationnalisation des solutions en intelligence d’affaires en partenariat avec les intervenants impliqués en assurant les arrimages transversaux requis, notamment au niveau des stratégies d’essais, des tests et de l’intégration de la documentation au référentiel;
• Assurer le transfert d’expertise aux équipes permanentes en place;
• Contribuer à la gestion de la performance de l’entrepôt de données;
• Mise en place des pipelines déploiement automatisés / DevOps.


Ton profil :

• Posséder 4 ans d’expérience avec la programmation sous MS SQL Server;
• Expérience avec Google/Funnel/Big Query;
• Posséder 4 ans d’expérience avec ETL;
• Posséder 4 ans d’expérience avec l’intégration de données venant de sources variées (API, JSON, XML, BD relationnel, métadonnées, etc.);
• Posséder 3 années d’expérience dans des projets utilisant la méthodologie SCRUM/Agile;
• Avoir une connaissance à jour des bases de données, des logiciels et outils technologiques généralement utilisés dans son domaine, des processus ETL (Extraction, Transformation et Chargement), ainsi que de la programmation, du traitement et de la modélisation de données.


Rencontrons-nous!
Notre projet commun vous plait ?
Postulez ici et rejoignez notre équipe! 


Nos Plus
• Une politique CARE sur-mesure déployée par nos équipes RH pour nos collaborateurs.
• Notre charte de la Diversité
• Des activités entre collègues comme des Teambuilding ou des 5@7",0,In Development,Hind BENABBOU,https://www.linkedin.com/in/hind-benabbou-99509345,Previous resume,True,2024-09-04 10:47:33.293451,2024-09-05 10:47:38.276327,https://www.linkedin.com/jobs/view/3996451517,Easy Applied,"{('numéro de téléphone portable', '**********', 'text', '**********'), ('depuis combien d’années utilisez-vous google bigquery ?', '0', 'text', '0'), ('depuis combien d’années utilisez-vous extract, transform, load (etl) ?', '4', 'text', '4'), ('Code pays [  ""Sélectionnez une option"", ""Canada (+1)"", ""Afghanistan (+93)"", ""Afrique du Sud (+27)"", ""Albanie (+355)"", ""Algérie (+213)"", ""Allemagne (+49)"", ""Andorra (+376)"", ""Angola (+244)"", ""Anguilla (+1)"", ""Antarctique (+0)"", ""Antigua-et-Barbuda (+1)"", ""Antilles néerlandaises (+0)"", ""Arabie Saoudite (+966)"", ""Argentine (+54)"", ""Arménie (+374)"", ""Aruba (+297)"", ""Australie (+61)"", ""Autriche (+43)"", ""Azerbaïdjan (+994)"", ""Bahamas (+1)"", ""Bahreïn (+973)"", ""Bangladesh (+880)"", ""Barbade (+1)"", ""Belgique (+32)"", ""Belize (+501)"", ""Bermudes (+1)"", ""Bhoutan (+975)"", ""Biélorussie (+375)"", ""Bolivie (+591)"", ""Bosnie-Herzégovine (+387)"", ""Botswana (+267)"", ""Brunei (+673)"", ""Brésil (+55)"", ""Bulgarie (+359)"", ""Burkina Faso (+226)"", ""Burundi (+257)"", ""Bénin (+229)"", ""Cambodge (+855)"", ""Cameroun (+237)"", ""Cap Vert (+238)"", ""Chili (+56)"", ""Chine (+86)"", ""Chypre (+357)"", ""Cité du Vatican (Saint-Siège) (+39)"", ""Colombie (+57)"", ""Comores (+269)"", ""Congo (+242)"", ""Corée du Nord (+850)"", ""Corée du Sud (+82)"", ""Costa Rica (+506)"", ""Croatie (+385)"", ""Cuba (+53)"", ""Côte d\'Ivoire (+225)"", ""Danemark (+45)"", ""Djibouti (+253)"", ""Dominique (+1)"", ""Espagne (+34)"", ""Estonie (+372)"", ""Fidji (+679)"", ""Finlande (+358)"", ""France (+33)"", ""Fédération de Russie (+7)"", ""Gabon (+241)"", ""Gambie (+220)"", ""Ghana (+233)"", ""Gibraltar (+350)"", ""Grenade (+1)"", ""Groenland (+299)"", ""Grèce (+30)"", ""Guadeloupe (+590)"", ""Guam (+1)"", ""Guatemala (+502)"", ""Guernesey (+44)"", ""Guinée (+224)"", ""Guinée équatoriale (+240)"", ""Guinée-Bissau (+245)"", ""Guyana (+592)"", ""Guyane française (+594)"", ""Géorgie (+995)"", ""Haïti (+509)"", ""Honduras (+504)"", ""Hong Kong (+852)"", ""Hongrie (+36)"", ""Inde (+91)"", ""Indonésie (+62)"", ""Irak (+964)"", ""Iran (+98)"", ""Irlande (+353)"", ""Islande (+354)"", ""Israël (+972)"", ""Italie (+39)"", ""Jamaïque (+1)"", ""Japon (+81)"", ""Jersey (+44)"", ""Jordanie (+962)"", ""Kazakhstan (+7)"", ""Kenya (+254)"", ""Kirghizistan (+996)"", ""Kiribati (+686)"", ""Kosovo (+383)"", ""Koweït (+965)"", ""Laos (+856)"", ""Lesotho (+266)"", ""Lettonie (+371)"", ""Liban (+961)"", ""Liberia (+231)"", ""Libye (+218)"", ""Liechtenstein (+423)"", ""Lituanie (+370)"", ""Luxembourg (+352)"", ""Macao (+853)"", ""Macédoine (+389)"", ""Madagascar (+261)"", ""Malaisie (+60)"", ""Malawi (+265)"", ""Maldives (+960)"", ""Mali (+223)"", ""Malte (+356)"", ""Maroc (+212)"", ""Martinique (+596)"", ""Maurice (+230)"", ""Mauritanie (+222)"", ""Mayotte (+262)"", ""Mexique (+52)"", ""Moldavie (+373)"", ""Monaco (+377)"", ""Mongolie (+976)"", ""Montserrat (+1)"", ""Monténégro (+382)"", ""Mozambique (+258)"", ""Myanmar (Birmanie) (+95)"", ""Namibie (+264)"", ""Nations caribéennes (+0)"", ""Nauru (+674)"", ""Nicaragua (+505)"", ""Niger (+227)"", ""Nigeria (+234)"", ""Niue (+683)"", ""Norfolk (+672)"", ""Norvège (+47)"", ""Nouvelle-Calédonie (+687)"", ""Nouvelle-Zélande (+64)"", ""Népal (+977)"", ""Oman (+968)"", ""Ouganda (+256)"", ""Ouzbékistan (+998)"", ""Pakistan (+92)"", ""Palau (+680)"", ""Panama (+507)"", ""Papouasie-Nouvelle-Guinée (+675)"", ""Paraguay (+595)"", ""Pays-Bas (+31)"", ""Philippines (+63)"", ""Pitcairn (+0)"", ""Pologne (+48)"", ""Polynésie française (+689)"", ""Porto Rico (+1)"", ""Portugal (+351)"", ""Pérou (+51)"", ""Qatar (+974)"", ""Roumanie (+40)"", ""Royaume-Uni (+44)"", ""Rwanda (+250)"", ""République Centrafricaine (+236)"", ""République Démocratique du Congo (+243)"", ""République dominicaine (+1)"", ""République tchèque (+420)"", ""Réunion (+262)"", ""Sahara occidental (+212)"", ""Saint Pierre et Miquelon (+508)"", ""Saint-Christophe-et-Niévès (+1)"", ""Saint-Marin (+378)"", ""Saint-Vincent-et-les-Grenadines (+1)"", ""Sainte-Hélène (+290)"", ""Sainte-Lucie (+1)"", ""Salvador (+503)"", ""Samoa (+685)"", ""Samoa (U.S.) (+1)"", ""Sao Tomé-et-Principe (+239)"", ""Serbie (+381)"", ""Serbie et Monténégro (+0)"", ""Seychelles (+248)"", ""Sierra Leone (+232)"", ""Singapour (+65)"", ""Slovaquie (+421)"", ""Slovénie (+386)"", ""Somalie (+252)"", ""Soudan (+249)"", ""Soudan du Sud (+211)"", ""Sri Lanka (+94)"", ""Suisse (+41)"", ""Suriname (+597)"", ""Suède (+46)"", ""Svalbard et Jan Mayen (+47)"", ""Swaziland (+268)"", ""Syrie (+963)"", ""Sénégal (+221)"", ""Tadjikistan (+992)"", ""Tanzanie (+255)"", ""Taïwan (+886)"", ""Tchad (+235)"", ""Territoire Britannique de l\'Océan Indien (+246)"", ""Territoires Français du Sud (+0)"", ""Territoires palestiniens (+970)"", ""Thaïlande (+66)"", ""Timor oriental (+670)"", ""Togo (+228)"", ""Tokelau (+690)"", ""Tonga (+676)"", ""Trinité-et-Tobago (+1)"", ""Tunisie (+216)"", ""Turkménistan (+993)"", ""Turquie (+90)"", ""Tuvalu (+688)"", ""Ukraine (+380)"", ""Uruguay (+598)"", ""Vanuatu (+678)"", ""Venezuela (+58)"", ""Vietnam (+84)"", ""Wallis et Futuna (+681)"", ""Yémen (+967)"", ""Zambie (+260)"", ""Zimbabwe (+263)"", ""Égypte (+20)"", ""Émirats arabes unis (+971)"", ""Équateur (+593)"", ""Érythrée (+291)"", ""États Fédérés de Micronésie (+691)"", ""États-Unis (+1)"", ""Éthiopie (+251)"", ""Île Bouvet (+0)"", ""Île Christmas (+61)"", ""Île Heard et îles McDonald (+0)"", ""Île de Man (+44)"", ""Îles Aland (+358)"", ""Îles Caïman (+1)"", ""Îles Cocos (Keeling) (+61)"", ""Îles Cook (+682)"", ""Îles Féroé (+298)"", ""Îles Malouines (Falkland) (+500)"", ""Îles Mariannes du nord (+1)"", ""Îles Marshall (+692)"", ""Îles Salomon (+677)"", ""Îles Turques et Caïques (+1)"", ""Îles Vierges (U.S.A.) (+1)"", ""Îles Vierges (britanniques) (+1)"", ""Îles de Géorgie du Sud et îles Sandwich du Sud (+0)"", ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('Êtes-vous légalement autorisé(e) à travailler dans le pays suivant ? Canada [  ""Oui""<Yes>, ""Non""<No>, ]', '""Oui""<Yes>', 'radio', '""Oui""<Yes>'), ('Adresse e-mail [  ""Sélectionnez une option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>')}",In Development
4014160062,"Intermediate ServiceNow GRC Developer
Intermediate ServiceNow GRC Developer with verification",Nexus Systems Group,"Greater Toronto Area, Canada",Hybrid,"About the job
ServiceNow GRC Developer

The ServiceNow GRC Developer will be responsible for technical design, code and architecture, and work with key business units to build solutions, processes, flows, supporting maintenance, continual service improvement, and new capabilities on the ServiceNow platform, mainly the Integrated Risk management and Vendor Risk Management modules. The responsibilities include: Developing, configuring, and customizing the ServiceNow platform to fit business requirements (field changes, report templates, tabling, bulk data imports).
Integrating Service Now modules with other systems (SAP Ariba).
Creating rational, competent-based technological designs from functional specifications.
Collaborating with cross-functional teams to identify and prioritize business requirements.
Writing, reviewing, and documenting clean, efficient, and maintainable code and application functionality.
Performing quality control checks.
Estimating, organizing, planning, and prioritizing testing-related tasks in accordance with change management procedures.
Debugging and fixing technical problems.",0,In Development,"Adam Murphy 
Adam Murphy is verified",https://www.linkedin.com/in/adam-murphy-9aa1b629,Previous resume,False,2024-09-04 10:47:53.770243,2024-09-05 10:47:56.588276,https://www.linkedin.com/jobs/view/4014160062,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('how many years of work experience do you have with governance, risk management, and compliance (grc)?', '7', 'text', ''), ('how many years of work experience do you have with servicenow?', '7', 'text', ''), ('mobile phone number', '**********', 'text', '**********'), ('Experiencing developing, configuring, and customizing the ServiceNow platform to fit business requirements (field changes, report templates, tabling, bulk data imports [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
**********,"Security Engineer
Security Engineer with verification",Insight Global,"Toronto, ON",Hybrid,"About the job
JOB DESCRIPTION
Insight Global is looking for a Security Engineer to join a 6 month contract to start with possibility of extension with one of Canada's top 5 banks. You’ll be a key role in the Data Protection Services team, an innovative team with many brand new services provided at an enterprise level, to provide data protection related services such as Data Element Level Protection (DELP), Test Data As a Service (TDaaS), Data Discovery and Remediation (DDaR) and Digital Certificate Lifecycle Manager (DCLM) services. As an Senior Security Engineer, you will work individually or lead a small development team to work with business partners, QA, vendor and AO partner on design, integration, configuration, development, implementation, troubleshooting and providing PROD support. • As a tech lead, work with partners to design, build, configure and deploy an Azure ETL pipeline using ADF, Azure Blob/ADLS, Azure SQL and Databricks • As a tech lead, work with Protegrity vendor to establish Protegrity Cloud Protect API using Azure App Service (v3 Linux) and Azure APIM • Integrate Azure ETL pipeline to consume Protegrity Cloud Protect API via APIM • Follow Agile methodology to setup environment, configure, test, debug, and document in order to ensure successful implementation and maintenance of the service during/post project • Document the solution design and prepare playbook for other team members to follow",0,In Development,"Adam (AJ) Abraham 
Adam (AJ) Abraham is verified",https://www.linkedin.com/in/adam-aj-abraham-473b5b171,Previous resume,False,2024-08-31 10:48:01.066371,2024-09-05 10:48:04.744803,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('Do you have knowledge or experience within DevOps? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('mobile phone number', '**********', 'text', '**********'), ('how many years of experience do you have working as a security engineer or similar roles?', '7', 'text', ''), ('Do you have strong knowledge of Linux OS, Spark and Python? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Do you have a strong understanding of firewalls, load balancers, LDAP/Active Directory? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Do you have experience with Azure ETL and Azure Technologies? Specifically designing and/or implementing an Azure ETL. [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Are you okay to come on-site in Toronto 1 day a week? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Canada (+1)', 'select', 'Canada (+1)')}",In Development
4011162630,"Architecte de Solutions - Snowflake / Horizon
Architecte de Solutions - Snowflake / Horizon with verification",Systematix,"Montreal, QC",Hybrid,"About the job
Télétravail: Hybride requis de 2 -3 jours/semaines au bureau mais pourrait être plus.
 Bilinguisme: Langue principale Français et l'anglais est nécessaire.

 L'équipe d'architecture de solutions dans l'Écosystème de Données, Intelligence Artificielle, Domaine Clients, Marketing et Plate-forme SAP est à la recherche d'un(e) Architecte de Solutions - Snowflake / Horizon. 

Rôle :
Concevoir des solutions technologiques pour la plateforme analytique Horizon, qui opère sur le cloud AWS, spécifiquement dans un entrepôt de données utilisant la technologie Snowflake, principalement pour l'aspect données.
Maîtriser et documenter de manière détaillée les flux de données menant à un Data Vault via l'outil DBT, supportant les processus d'affaires impactés par votre solution.
Automatiser le processus via Terraform.
Intégrer les solutions développées à l'écosystème actuel tout en le faisant évoluer.
Veiller à ce que les solutions développées respectent les principes d'architecture d'entreprise et documenter la dette technologique lorsque une solution s'en écarte.
Détails :
Créer, challenger ou contribuer au développement de solutions et aux autres tâches nécessaires selon les besoins pour assurer la livraison de solutions.
Définir des critères d'évaluation technique pour la sélection de produits / technologies et pour déterminer les approches techniques à privilégier pour que les solutions portent leurs fruits, au sein d'une conception de systèmes cohérents.
Contribuer aux exercices de démonstration de faisabilité pour l'adoption de nouvelles technologies.
Participer à la conception et à la réalisation de la stratégie de développement et de test.
Comprendre et déterminer les risques et les enjeux liés à la solution choisie.
Respecter les normes et standards et mettre à jour la documentation.
Participer à la mise en production et au support de la solution pendant la phase de stabilisation.
Utiliser votre influence pour recommander des méthodes d'amélioration de la fiabilité et de l'efficience des solutions.

Profil recherché :
Baccalauréat complété, connexe au secteur d'activité, et 6 années d'expérience pertinente ou Maîtrise complétée, connexe au secteur d'activité et 4 années d'expérience pertinente.
Expertise forte en gestion des données et architecture de données.
Bonne connaissance de plusieurs des technologies/outils suivants (rating relatif) :
Architecture et ingénierie des données : 10/10
Compréhension rapide de codes SQL : 9/10
Snowflake : 9/10
Data Vault : 8/10
AWS cloud and services: 8/10
Poser les bonnes questions : 8/10
DBT : 7/10 (data build tool)
Compétences relationnelles : 7/10
Communication claire : 7/10
Français : 7/10
Anglais : 6/10
Terraform : 6/10
Python : 5/10
Databricks : 3/10
EKS : 3/10
Expérience en tant qu'architecte de solutions dans une organisation complexe.
Expérience en automatisation de flux de données.
Capacité à travailler au niveau technique le plus élevé de la majorité des phases d'analyse des systèmes tout en tenant compte des conséquences administratives de l'application de la technologie dans l'environnement organisationnel actuel et futur.
Habileté à influencer et mobiliser positivement les parties prenantes.
Fortes aptitudes analytiques.
Leadership, initiative, autonomie et habileté de communication.",0,In Development,"Elizabeth Slanke 
Elizabeth Slanke is verified",https://www.linkedin.com/in/elizabeth-slanke-b45005201,Previous resume,False,2024-08-30 10:48:35.540809,2024-09-05 10:48:40.459603,https://www.linkedin.com/jobs/view/4011162630,Easy Applied,"{('numéro de téléphone portable', '**********', 'text', '**********'), ('Code pays [  ""Sélectionnez une option"", ""Canada (+1)"", ""Afghanistan (+93)"", ""Afrique du Sud (+27)"", ""Albanie (+355)"", ""Algérie (+213)"", ""Allemagne (+49)"", ""Andorra (+376)"", ""Angola (+244)"", ""Anguilla (+1)"", ""Antarctique (+0)"", ""Antigua-et-Barbuda (+1)"", ""Antilles néerlandaises (+0)"", ""Arabie Saoudite (+966)"", ""Argentine (+54)"", ""Arménie (+374)"", ""Aruba (+297)"", ""Australie (+61)"", ""Autriche (+43)"", ""Azerbaïdjan (+994)"", ""Bahamas (+1)"", ""Bahreïn (+973)"", ""Bangladesh (+880)"", ""Barbade (+1)"", ""Belgique (+32)"", ""Belize (+501)"", ""Bermudes (+1)"", ""Bhoutan (+975)"", ""Biélorussie (+375)"", ""Bolivie (+591)"", ""Bosnie-Herzégovine (+387)"", ""Botswana (+267)"", ""Brunei (+673)"", ""Brésil (+55)"", ""Bulgarie (+359)"", ""Burkina Faso (+226)"", ""Burundi (+257)"", ""Bénin (+229)"", ""Cambodge (+855)"", ""Cameroun (+237)"", ""Cap Vert (+238)"", ""Chili (+56)"", ""Chine (+86)"", ""Chypre (+357)"", ""Cité du Vatican (Saint-Siège) (+39)"", ""Colombie (+57)"", ""Comores (+269)"", ""Congo (+242)"", ""Corée du Nord (+850)"", ""Corée du Sud (+82)"", ""Costa Rica (+506)"", ""Croatie (+385)"", ""Cuba (+53)"", ""Côte d\'Ivoire (+225)"", ""Danemark (+45)"", ""Djibouti (+253)"", ""Dominique (+1)"", ""Espagne (+34)"", ""Estonie (+372)"", ""Fidji (+679)"", ""Finlande (+358)"", ""France (+33)"", ""Fédération de Russie (+7)"", ""Gabon (+241)"", ""Gambie (+220)"", ""Ghana (+233)"", ""Gibraltar (+350)"", ""Grenade (+1)"", ""Groenland (+299)"", ""Grèce (+30)"", ""Guadeloupe (+590)"", ""Guam (+1)"", ""Guatemala (+502)"", ""Guernesey (+44)"", ""Guinée (+224)"", ""Guinée équatoriale (+240)"", ""Guinée-Bissau (+245)"", ""Guyana (+592)"", ""Guyane française (+594)"", ""Géorgie (+995)"", ""Haïti (+509)"", ""Honduras (+504)"", ""Hong Kong (+852)"", ""Hongrie (+36)"", ""Inde (+91)"", ""Indonésie (+62)"", ""Irak (+964)"", ""Iran (+98)"", ""Irlande (+353)"", ""Islande (+354)"", ""Israël (+972)"", ""Italie (+39)"", ""Jamaïque (+1)"", ""Japon (+81)"", ""Jersey (+44)"", ""Jordanie (+962)"", ""Kazakhstan (+7)"", ""Kenya (+254)"", ""Kirghizistan (+996)"", ""Kiribati (+686)"", ""Kosovo (+383)"", ""Koweït (+965)"", ""Laos (+856)"", ""Lesotho (+266)"", ""Lettonie (+371)"", ""Liban (+961)"", ""Liberia (+231)"", ""Libye (+218)"", ""Liechtenstein (+423)"", ""Lituanie (+370)"", ""Luxembourg (+352)"", ""Macao (+853)"", ""Macédoine (+389)"", ""Madagascar (+261)"", ""Malaisie (+60)"", ""Malawi (+265)"", ""Maldives (+960)"", ""Mali (+223)"", ""Malte (+356)"", ""Maroc (+212)"", ""Martinique (+596)"", ""Maurice (+230)"", ""Mauritanie (+222)"", ""Mayotte (+262)"", ""Mexique (+52)"", ""Moldavie (+373)"", ""Monaco (+377)"", ""Mongolie (+976)"", ""Montserrat (+1)"", ""Monténégro (+382)"", ""Mozambique (+258)"", ""Myanmar (Birmanie) (+95)"", ""Namibie (+264)"", ""Nations caribéennes (+0)"", ""Nauru (+674)"", ""Nicaragua (+505)"", ""Niger (+227)"", ""Nigeria (+234)"", ""Niue (+683)"", ""Norfolk (+672)"", ""Norvège (+47)"", ""Nouvelle-Calédonie (+687)"", ""Nouvelle-Zélande (+64)"", ""Népal (+977)"", ""Oman (+968)"", ""Ouganda (+256)"", ""Ouzbékistan (+998)"", ""Pakistan (+92)"", ""Palau (+680)"", ""Panama (+507)"", ""Papouasie-Nouvelle-Guinée (+675)"", ""Paraguay (+595)"", ""Pays-Bas (+31)"", ""Philippines (+63)"", ""Pitcairn (+0)"", ""Pologne (+48)"", ""Polynésie française (+689)"", ""Porto Rico (+1)"", ""Portugal (+351)"", ""Pérou (+51)"", ""Qatar (+974)"", ""Roumanie (+40)"", ""Royaume-Uni (+44)"", ""Rwanda (+250)"", ""République Centrafricaine (+236)"", ""République Démocratique du Congo (+243)"", ""République dominicaine (+1)"", ""République tchèque (+420)"", ""Réunion (+262)"", ""Sahara occidental (+212)"", ""Saint Pierre et Miquelon (+508)"", ""Saint-Christophe-et-Niévès (+1)"", ""Saint-Marin (+378)"", ""Saint-Vincent-et-les-Grenadines (+1)"", ""Sainte-Hélène (+290)"", ""Sainte-Lucie (+1)"", ""Salvador (+503)"", ""Samoa (+685)"", ""Samoa (U.S.) (+1)"", ""Sao Tomé-et-Principe (+239)"", ""Serbie (+381)"", ""Serbie et Monténégro (+0)"", ""Seychelles (+248)"", ""Sierra Leone (+232)"", ""Singapour (+65)"", ""Slovaquie (+421)"", ""Slovénie (+386)"", ""Somalie (+252)"", ""Soudan (+249)"", ""Soudan du Sud (+211)"", ""Sri Lanka (+94)"", ""Suisse (+41)"", ""Suriname (+597)"", ""Suède (+46)"", ""Svalbard et Jan Mayen (+47)"", ""Swaziland (+268)"", ""Syrie (+963)"", ""Sénégal (+221)"", ""Tadjikistan (+992)"", ""Tanzanie (+255)"", ""Taïwan (+886)"", ""Tchad (+235)"", ""Territoire Britannique de l\'Océan Indien (+246)"", ""Territoires Français du Sud (+0)"", ""Territoires palestiniens (+970)"", ""Thaïlande (+66)"", ""Timor oriental (+670)"", ""Togo (+228)"", ""Tokelau (+690)"", ""Tonga (+676)"", ""Trinité-et-Tobago (+1)"", ""Tunisie (+216)"", ""Turkménistan (+993)"", ""Turquie (+90)"", ""Tuvalu (+688)"", ""Ukraine (+380)"", ""Uruguay (+598)"", ""Vanuatu (+678)"", ""Venezuela (+58)"", ""Vietnam (+84)"", ""Wallis et Futuna (+681)"", ""Yémen (+967)"", ""Zambie (+260)"", ""Zimbabwe (+263)"", ""Égypte (+20)"", ""Émirats arabes unis (+971)"", ""Équateur (+593)"", ""Érythrée (+291)"", ""États Fédérés de Micronésie (+691)"", ""États-Unis (+1)"", ""Éthiopie (+251)"", ""Île Bouvet (+0)"", ""Île Christmas (+61)"", ""Île Heard et îles McDonald (+0)"", ""Île de Man (+44)"", ""Îles Aland (+358)"", ""Îles Caïman (+1)"", ""Îles Cocos (Keeling) (+61)"", ""Îles Cook (+682)"", ""Îles Féroé (+298)"", ""Îles Malouines (Falkland) (+500)"", ""Îles Mariannes du nord (+1)"", ""Îles Marshall (+692)"", ""Îles Salomon (+677)"", ""Îles Turques et Caïques (+1)"", ""Îles Vierges (U.S.A.) (+1)"", ""Îles Vierges (britanniques) (+1)"", ""Îles de Géorgie du Sud et îles Sandwich du Sud (+0)"", ]', 'Canada (+1)', 'select', 'Canada (+1)'), ('depuis combien d’années utilisez-vous architecture ?', '7', 'text', ''), ('Adresse e-mail [  ""Sélectionnez une option"", ""<EMAIL>"", ]', '<EMAIL>', 'select', '<EMAIL>'), ('depuis combien d’années utilisez-vous sql ?', '3', 'text', '3')}",In Development
